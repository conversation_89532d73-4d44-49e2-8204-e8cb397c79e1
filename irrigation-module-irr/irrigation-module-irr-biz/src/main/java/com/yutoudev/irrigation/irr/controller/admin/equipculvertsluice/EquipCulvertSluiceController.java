package com.yutoudev.irrigation.irr.controller.admin.equipculvertsluice;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.equipculvertsluice.vo.*;
import com.yutoudev.irrigation.irr.convert.equipculvertsluice.EquipCulvertSluiceConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipculvertsluice.EquipCulvertSluiceDO;
import com.yutoudev.irrigation.irr.service.equipculvertsluice.EquipCulvertSluiceService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 *
 * 站点涵闸配置
 * @description 管理后台-站点涵闸配置controller
 * <AUTHOR>
 * @time 2024-08-17 23:16:50
 *
 */
@RestController
@RequestMapping("/irr/equip-culvert-sluice")
@Validated
public class EquipCulvertSluiceController {

    private static final String MODULE_NAME = "站点涵闸配置";

    @Resource
    private EquipCulvertSluiceService<EquipCulvertSluiceDO> equipCulvertSluiceService;

    /**
     * 创建站点涵闸配置
     * @description 单个对象保存
     * @param createReqVO EquipCulvertSluiceCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody EquipCulvertSluiceCreateReqVO createReqVO) {
        return success(equipCulvertSluiceService.create(createReqVO));
    }

    /**
     * 批量创建站点涵闸配置
     * @description 多个对象保存
     * @param lists  EquipCulvertSluiceCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<EquipCulvertSluiceCreateReqVO> lists) {
        return success(equipCulvertSluiceService.createBatch(lists));
    }

    /**
     * 更新站点涵闸配置
     * @description 单个对象修改
     * @param updateReqVO EquipCulvertSluiceUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody EquipCulvertSluiceUpdateReqVO updateReqVO) {
        equipCulvertSluiceService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新站点涵闸配置
     * @description 批量更新
     * @param lists 批量更新列表 EquipCulvertSluiceUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<EquipCulvertSluiceUpdateReqVO> lists) {
        return success(equipCulvertSluiceService.updateBatch(lists));
    }

    /**
     * 删除站点涵闸配置
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        equipCulvertSluiceService.delete(id);
        return success(true);
    }

    /**
     * 批量删除站点涵闸配置
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(equipCulvertSluiceService.deleteBatch(ids));
    }

    /**
     * 获得站点涵闸配置详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<EquipCulvertSluiceDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<EquipCulvertSluiceDetailRespVO> get(@RequestParam("id") Long id) {
        EquipCulvertSluiceDO equipCulvertSluice = equipCulvertSluiceService.get(id);
        return success(EquipCulvertSluiceConvert.INSTANCE.convertDetail(equipCulvertSluice));
    }

    /**
     * 站点涵闸配置列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 EquipCulvertSluiceQueryReqVO
     * @return CommonResult<List<EquipCulvertSluiceRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<EquipCulvertSluiceRespVO>> getList(@RequestQueryParam EquipCulvertSluiceQueryReqVO queryReqVO) {
        List<EquipCulvertSluiceDO> list = equipCulvertSluiceService.getList(queryReqVO);
        return success(EquipCulvertSluiceConvert.INSTANCE.convertList(list));
    }

    /**
     * 站点涵闸配置分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 EquipCulvertSluicePageReqVO
     * @return CommonResult<PageResult<EquipCulvertSluiceRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<EquipCulvertSluiceRespVO>> page(@RequestQueryParam EquipCulvertSluicePageReqVO pageVO) {
        PageResult<EquipCulvertSluiceDO> pageResult = equipCulvertSluiceService.page(pageVO);
        return success(EquipCulvertSluiceConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出站点涵闸配置Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 EquipCulvertSluiceExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam EquipCulvertSluiceExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<EquipCulvertSluiceDO> list = equipCulvertSluiceService.getList(queryReqVO);
        // 导出 Excel
        List<EquipCulvertSluiceExcelVO> datas = EquipCulvertSluiceConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "站点涵闸配置", "xlsx"), queryReqVO.getExportSheetName(),
                                                    EquipCulvertSluiceExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入站点涵闸配置模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "站点涵闸配置-导入模版.xls", "sheet1", EquipCulvertSluiceExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入站点涵闸配置Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:equip-culvert-sluice:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<EquipCulvertSluiceExcelVO> list = ExcelUtils.read(file, EquipCulvertSluiceExcelVO.class);
        return success(equipCulvertSluiceService.importExcel(list, isUpdate));
    }
}