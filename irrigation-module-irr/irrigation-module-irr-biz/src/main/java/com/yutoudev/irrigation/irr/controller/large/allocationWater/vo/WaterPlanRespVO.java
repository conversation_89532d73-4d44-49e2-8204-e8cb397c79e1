package com.yutoudev.irrigation.irr.controller.large.allocationWater.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 配水计划ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-配水计划ResponseVO
 * @time 2025-03-25
 */
@Data
@ToString(callSuper = true)
public class WaterPlanRespVO {
    /**
     * 渠道ID
     */
    private Long chanId;
    /**
     * 渠道名称
     */
    private String chanName;
    /**
     * 配水
     */
    private Double water;
    /**
     * 定额
     */
    private Double quota;
    /**
     * 累计用水
     */
    private Double totalWater;
    /**
     * 剩余可用水量
     */
    private Double remaining;
}
