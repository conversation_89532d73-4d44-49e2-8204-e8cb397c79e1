package com.yutoudev.irrigation.irr.controller.admin.allocationplanmonth;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.constraints.*;
import javax.validation.*;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;


import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yutoudev.irrigation.irr.controller.admin.allocationplanmonth.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationplanmonth.AllocationPlanMonthDO;
import com.yutoudev.irrigation.irr.convert.allocationplanmonth.AllocationPlanMonthConvert;
import com.yutoudev.irrigation.irr.service.allocationplanmonth.AllocationPlanMonthService;


/**
 *
 * 配水调度月计划
 * @description 管理后台-配水调度月计划controller
 * <AUTHOR>
 * @time 2024-07-23 14:54:39
 *
 */
@RestController
@RequestMapping("/irr/allocation-plan-month")
@Validated
public class AllocationPlanMonthController {

    private static final String MODULE_NAME = "配水调度月计划";

    @Resource
    private AllocationPlanMonthService<AllocationPlanMonthDO> allocationPlanMonthService;

    /**
     * 创建配水调度月计划
     * @description 单个对象保存
     * @param createReqVO AllocationPlanMonthCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody AllocationPlanMonthCreateReqVO createReqVO) {
        return success(allocationPlanMonthService.create(createReqVO));
    }

    /**
     * 批量创建配水调度月计划
     * @description 多个对象保存
     * @param lists  AllocationPlanMonthCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<AllocationPlanMonthCreateReqVO> lists) {
        return success(allocationPlanMonthService.createBatch(lists));
    }

    /**
     * 更新配水调度月计划
     * @description 单个对象修改
     * @param updateReqVO AllocationPlanMonthUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody AllocationPlanMonthUpdateReqVO updateReqVO) {
        allocationPlanMonthService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新配水调度月计划
     * @description 批量更新
     * @param lists 批量更新列表 AllocationPlanMonthUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<AllocationPlanMonthUpdateReqVO> lists) {
        return success(allocationPlanMonthService.updateBatch(lists));
    }

    /**
     * 删除配水调度月计划
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        allocationPlanMonthService.delete(id);
        return success(true);
    }

    /**
     * 批量删除配水调度月计划
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(allocationPlanMonthService.deleteBatch(ids));
    }

    /**
     * 获得配水调度月计划详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<AllocationPlanMonthDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<AllocationPlanMonthDetailRespVO> get(@RequestParam("id") Long id) {
        AllocationPlanMonthDO allocationPlanMonth = allocationPlanMonthService.get(id);
        return success(AllocationPlanMonthConvert.INSTANCE.convertDetail(allocationPlanMonth));
    }

    /**
     * 配水调度月计划列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 AllocationPlanMonthQueryReqVO
     * @return CommonResult<List<AllocationPlanMonthRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<AllocationPlanMonthRespVO>> getList(@RequestQueryParam AllocationPlanMonthQueryReqVO queryReqVO) {
        List<AllocationPlanMonthDO> list = allocationPlanMonthService.getList(queryReqVO);
        return success(AllocationPlanMonthConvert.INSTANCE.convertList(list));
    }

    /**
     * 配水调度月计划分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 AllocationPlanMonthPageReqVO
     * @return CommonResult<PageResult<AllocationPlanMonthRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<AllocationPlanMonthRespVO>> page(@RequestQueryParam AllocationPlanMonthPageReqVO pageVO) {
        PageResult<AllocationPlanMonthDO> pageResult = allocationPlanMonthService.page(pageVO);
        return success(AllocationPlanMonthConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出配水调度月计划Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 AllocationPlanMonthExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam AllocationPlanMonthExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<AllocationPlanMonthDO> list = allocationPlanMonthService.getList(queryReqVO);
        // 导出 Excel
        List<AllocationPlanMonthExcelVO> datas = AllocationPlanMonthConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "配水调度月计划", "xlsx"), queryReqVO.getExportSheetName(),
                                                    AllocationPlanMonthExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入配水调度月计划模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "配水调度月计划-导入模版.xls", "sheet1", AllocationPlanMonthExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入配水调度月计划Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-month:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<AllocationPlanMonthExcelVO> list = ExcelUtils.read(file, AllocationPlanMonthExcelVO.class);
        return success(allocationPlanMonthService.importExcel(list, isUpdate));
    }
}