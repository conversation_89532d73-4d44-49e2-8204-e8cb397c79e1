package com.yutoudev.irrigation.irr.controller.large.flood;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanEquipDetailRespVO;
import com.yutoudev.irrigation.irr.controller.large.flood.vo.RoutingChannelLargeRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.chanbase.ChanBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.flood.RoutingChannelDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measrealtime.MeasRealtimeDO;
import com.yutoudev.irrigation.irr.enums.ChanTypeEnum;
import com.yutoudev.irrigation.irr.enums.EquipSiteTypeEnum;
import com.yutoudev.irrigation.irr.service.chanbase.ChanBaseService;
import com.yutoudev.irrigation.irr.service.flood.RoutingChannelService;
import com.yutoudev.irrigation.irr.service.measrealtime.MeasRealtimeService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 渠道调洪演算
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/routing-channel")
@Validated
public class LargeRoutingChannelController {

    private static final String MODULE_NAME = "渠道调洪演算-大屏";

    @Resource
    private RoutingChannelService<RoutingChannelDO> routingChannelService;

    @Resource
    private ChanBaseService<ChanBaseDO> chanBaseService;

    @Resource
    private MeasRealtimeService<MeasRealtimeDO> measRealtimeService;

    /**
     * 渠道调洪演算列表
     *
     * @return CommonResult<List < RoutingChannelRespVO>> 列表响应VO
     */
    @GetMapping("/last-list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<RoutingChannelLargeRespVO>> getLastList() {
        List<Integer> chanTypeList = Arrays.asList(ChanTypeEnum.MAIN_TRUNK_CANAL.getType(), ChanTypeEnum.TRUNK_CANAL.getType());
        List<ChanBaseDO> chanList = chanBaseService.getListByTypes(chanTypeList);
        List<RoutingChannelLargeRespVO> respList = new ArrayList<>();
        for (ChanBaseDO chanBaseDO : chanList) {
            RoutingChannelDO routingChannelDO = routingChannelService.getLastByChanId(chanBaseDO.getId());
            RoutingChannelLargeRespVO routingChannelLargeRespVO = new RoutingChannelLargeRespVO();
            routingChannelLargeRespVO.setChanId(chanBaseDO.getId());
            routingChannelLargeRespVO.setChanName(chanBaseDO.getChanName());
            routingChannelLargeRespVO.setCalcDesignFlow(chanBaseDO.getDesFlow());
            routingChannelLargeRespVO.setOverflow(0);
            if (Objects.nonNull(routingChannelDO)) {
                routingChannelLargeRespVO.setCalcDesignFlow(routingChannelDO.getCalcDesignFlow());
                routingChannelLargeRespVO.setCalcStakeNumber(routingChannelDO.getCalcStakeNumber());
                routingChannelLargeRespVO.setOverflow(routingChannelDO.getOverflow());
                routingChannelLargeRespVO.setMaxOutFlow(routingChannelDO.getMaxOutFlow());
                routingChannelLargeRespVO.setMaxOutFlowTime(routingChannelDO.getMaxOutFlowTime());
                routingChannelLargeRespVO.setMaxInflow(routingChannelDO.getMaxOutFlow());
            }

            List<ChanEquipDetailRespVO> equipList = chanBaseService.listEquip(chanBaseDO.getId());
            for (ChanEquipDetailRespVO equipDetailRespVO : equipList) {
                if (Objects.equals(EquipSiteTypeEnum.MEAS.getType(), equipDetailRespVO.getEquip().getStType())) {
                    MeasRealtimeDO measRealtimeDO = measRealtimeService.getByCentralIdAndDevId(equipDetailRespVO.getCentralId(), equipDetailRespVO.getDevId());
                    routingChannelLargeRespVO.setIntervalFlow(measRealtimeDO.getV10().doubleValue());
                    break;
                }
            }
            respList.add(routingChannelLargeRespVO);
        }

        return success(respList);
    }

}