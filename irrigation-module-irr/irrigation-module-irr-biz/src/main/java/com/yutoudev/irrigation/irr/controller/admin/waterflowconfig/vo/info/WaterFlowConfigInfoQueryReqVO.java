package com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.info;

import lombok.*;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.waterflowconfig.WaterFlowConfigInfoDO;


/**
 *
 * 水流配置信息list查询RequestVO
 * @description 管理后台-水流配置信息list查询RequestVO，参数和 WaterFlowConfigInfoPageReqVO 是一致的
 * <AUTHOR>
 * @time 2024-08-13 15:15:34
 *
 */
@Data
public class WaterFlowConfigInfoQueryReqVO extends QueryCriteria<WaterFlowConfigInfoDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 水流配置ID
     * 
     */
    private Long waterFlowId;

    /**
     * 月
     * 
     */
    private String month;

    /**
     * 蒸发量
     * @mock mm/m²
     */
    private Double evaporation;

    /**
     * 损耗值
     * @mock 百分比
     */
    private Double loss;

    /**
     * 径流值
     * @mock 百分比
     */
    private Double runoff;

    /**
     * 径流时间
     * @mock 分钟
     */
    private Integer runoffTime;

}
