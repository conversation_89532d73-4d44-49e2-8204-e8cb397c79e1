package com.yutoudev.irrigation.irr.controller.large.map;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.map.vo.trajectory.MapTrajectoryDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.map.vo.trajectory.MapTrajectoryGisRespVO;
import com.yutoudev.irrigation.irr.controller.admin.map.vo.trajectory.MapTrajectoryQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.map.vo.trajectory.MapTrajectoryRespVO;
import com.yutoudev.irrigation.irr.convert.map.MapTrajectoryConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.map.MapTrajectoryDO;
import com.yutoudev.irrigation.irr.service.map.MapTrajectoryService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 地图轨迹-大屏
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/map-trajectory")
@Validated
public class LargeMapTrajectoryController {

    private static final String MODULE_NAME = "地图轨迹";

    @Resource
    private MapTrajectoryService<MapTrajectoryDO> mapTrajectoryService;

    /**
     * 获得地图轨迹详情
     *
     * @param id 编号 Long
     * @return CommonResult<MapTrajectoryDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:map-trajectory:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<MapTrajectoryDetailRespVO> get(@RequestParam("id") Long id) {
        MapTrajectoryDO mapTrajectory = mapTrajectoryService.get(id);
        return success(MapTrajectoryConvert.INSTANCE.convertDetail(mapTrajectory));
    }

    /**
     * 地图轨迹列表
     *
     * @param queryReqVO 查询条件 MapTrajectoryQueryReqVO
     * @return CommonResult<List < MapTrajectoryRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:map-trajectory:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<MapTrajectoryRespVO>> getList(@RequestQueryParam MapTrajectoryQueryReqVO queryReqVO) {
        List<MapTrajectoryDO> list = mapTrajectoryService.getList(queryReqVO);
        return success(MapTrajectoryConvert.INSTANCE.convertList(list));
    }

    /**
     * 地图轨迹列表
     *
     * @param queryReqVO 查询条件 MapTrajectoryQueryReqVO
     * @return CommonResult<List < MapTrajectoryRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/listGeo")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<MapTrajectoryGisRespVO>> getListGeo(@RequestQueryParam MapTrajectoryQueryReqVO queryReqVO) {
        List<MapTrajectoryDO> list = mapTrajectoryService.getList(queryReqVO);
        return success(MapTrajectoryConvert.INSTANCE.convertListGeo(list));
    }
}