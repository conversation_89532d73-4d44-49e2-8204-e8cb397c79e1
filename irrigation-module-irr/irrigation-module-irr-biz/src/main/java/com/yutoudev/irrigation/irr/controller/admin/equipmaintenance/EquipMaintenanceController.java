package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo.*;
import com.yutoudev.irrigation.irr.convert.equipmaintenance.EquipMaintenanceConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipmaintenance.EquipMaintenanceDO;
import com.yutoudev.irrigation.irr.service.equipmaintenance.EquipMaintenanceService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 设备维护
 *
 * <AUTHOR>
 * @description 管理后台-设备维护controller
 * @time 2024-10-27 10:33:13
 */
@RestController
@RequestMapping("/irr/equip-maintenance")
@Validated
public class EquipMaintenanceController {

    private static final String MODULE_NAME = "设备维护";

    @Resource
    private EquipMaintenanceService<EquipMaintenanceDO> equipMaintenanceService;

    /**
     * 创建设备维护
     *
     * @param createReqVO EquipMaintenanceCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody EquipMaintenanceCreateReqVO createReqVO) {
        return success(equipMaintenanceService.create(createReqVO));
    }

    /**
     * 批量创建设备维护
     *
     * @param lists EquipMaintenanceCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<EquipMaintenanceCreateReqVO> lists) {
        return success(equipMaintenanceService.createBatch(lists));
    }

    /**
     * 更新设备维护
     *
     * @param updateReqVO EquipMaintenanceUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody EquipMaintenanceUpdateReqVO updateReqVO) {
        equipMaintenanceService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新设备维护
     *
     * @param lists 批量更新列表 EquipMaintenanceUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<EquipMaintenanceUpdateReqVO> lists) {
        return success(equipMaintenanceService.updateBatch(lists));
    }

    /**
     * 删除设备维护
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        equipMaintenanceService.delete(id);
        return success(true);
    }

    /**
     * 批量删除设备维护
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(equipMaintenanceService.deleteBatch(ids));
    }

    /**
     * 获得设备维护详情
     *
     * @param id 编号 Long
     * @return CommonResult<EquipMaintenanceDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<EquipMaintenanceDetailRespVO> get(@RequestParam("id") Long id) {
        EquipMaintenanceDO equipMaintenance = equipMaintenanceService.get(id);
        return success(EquipMaintenanceConvert.INSTANCE.convertDetail(equipMaintenance));
    }

    /**
     * 设备维护列表
     *
     * @param queryReqVO 查询条件 EquipMaintenanceQueryReqVO
     * @return CommonResult<List < EquipMaintenanceRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<EquipMaintenanceRespVO>> getList(@RequestQueryParam EquipMaintenanceQueryReqVO queryReqVO) {
        List<EquipMaintenanceDO> list = equipMaintenanceService.getList(queryReqVO);
        return success(EquipMaintenanceConvert.INSTANCE.convertList(list));
    }

    /**
     * 设备维护分页
     *
     * @param pageVO 查询条件 EquipMaintenancePageReqVO
     * @return CommonResult<PageResult < EquipMaintenanceRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<EquipMaintenanceRespVO>> page(@RequestQueryParam EquipMaintenancePageReqVO pageVO) {
        PageResult<EquipMaintenanceDO> pageResult = equipMaintenanceService.page(pageVO);
        return success(EquipMaintenanceConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出设备维护Excel
     *
     * @param queryReqVO 查询条件 EquipMaintenanceExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam EquipMaintenanceExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<EquipMaintenanceDO> list = equipMaintenanceService.getList(queryReqVO);
        // 导出 Excel
        List<EquipMaintenanceExcelVO> datas = EquipMaintenanceConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "设备维护", "xlsx"), queryReqVO.getExportSheetName(),
                EquipMaintenanceExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入设备维护模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "设备维护-导入模版.xls", "sheet1", EquipMaintenanceExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入设备维护Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<EquipMaintenanceExcelVO> list = ExcelUtils.read(file, EquipMaintenanceExcelVO.class);
        return success(equipMaintenanceService.importExcel(list, isUpdate));
    }
}