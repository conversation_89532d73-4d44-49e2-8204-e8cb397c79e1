package com.yutoudev.irrigation.irr.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 用水单元用水类型
 * irr_user_use_type
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WaterUseTypeEnum {

    /**
     * 城乡生活用水
     */
    URBAN_AND_RURAL_DOMESTIC_WATER(1, "城乡生活用水"),

    /**
     * 城乡生产用水
     */
    URBAN_AND_RURAL_PRODUCTION_WATER(2, "城乡生产用水"),

    /**
     * 水力发电用水
     */
    HYDROPOWER_GENERATION_WATER(3, "水力发电用水"),
    /**
     * 自然生态用水
     */
    NATURAL_ECOLOGICAL_WATER(4, "自然生态用水"),
    /**
     * 农业灌溉用水
     */
    AGRICULTURAL_IRRIGATION_WATER(6, "农业灌溉用水");

    /**
     * 用水类型
     */
    private final Integer type;
    /**
     * 类型名称
     */
    private final String name;

    public static WaterUseTypeEnum getByType(Integer type) {
        return Arrays.stream(WaterUseTypeEnum.values())
                .filter(waterUseType -> waterUseType.getType().equals(type))
                .findFirst().orElse(null);
    }
}
