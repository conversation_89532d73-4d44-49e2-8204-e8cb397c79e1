package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo.EquipMaintenanceFileUpdateReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;
import java.util.List;


/**
 * 设备维护更新RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护更新RequestVO
 * @time 2024-10-27 10:33:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EquipMaintenanceUpdateReqVO extends EquipMaintenanceBaseVO {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;


    /**
     * 文件列表
     */
    private List<EquipMaintenanceFileUpdateReqVO> files;
}