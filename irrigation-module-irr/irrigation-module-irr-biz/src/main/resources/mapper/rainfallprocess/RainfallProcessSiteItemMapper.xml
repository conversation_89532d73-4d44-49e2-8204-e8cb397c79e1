<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.rainfallprocess.RainfallProcessSiteItemMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO `irr_rainfall_process_site_item` (
        `process_id`, `report_time`, `rainfall`, `cumulate_rainfall`,
        `evaporation`, `evapotranspiration`, `capacity`, `capacity_diff`,
        `yield`, `inflow`, `inflow_diff`, `surface_evaporation`,
        `runoff_depth`, `runoff_coef`
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.processId}, #{entity.reportTime}, #{entity.rainfall}, #{entity.cumulateRainfall},
            #{entity.evaporation}, #{entity.evapotranspiration}, #{entity.capacity}, #{entity.capacityDiff},
            #{entity.yield}, #{entity.inflow}, #{entity.inflowDiff}, #{entity.surfaceEvaporation},
            #{entity.runoffDepth}, #{entity.runoffCoef})
        </foreach>
        ON DUPLICATE KEY UPDATE
        `rainfall` = VALUES(`rainfall`),
        `cumulate_rainfall` = VALUES(`cumulate_rainfall`),
        `evaporation` = VALUES(`evaporation`),
        `evapotranspiration` = VALUES(`evapotranspiration`),
        `capacity` = VALUES(`capacity`),
        `capacity_diff` = VALUES(`capacity_diff`),
        `yield` = VALUES(`yield`),
        `inflow` = VALUES(`inflow`),
        `inflow_diff` = VALUES(`inflow_diff`),
        `surface_evaporation` = VALUES(`surface_evaporation`),
        `runoff_depth` = VALUES(`runoff_depth`),
        `runoff_coef` = VALUES(`runoff_coef`)
    </insert>
</mapper>
