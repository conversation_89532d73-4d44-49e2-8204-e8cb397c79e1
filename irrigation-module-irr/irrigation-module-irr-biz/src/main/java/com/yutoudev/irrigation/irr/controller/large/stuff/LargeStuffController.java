package com.yutoudev.irrigation.irr.controller.large.stuff;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.irr.controller.large.stuff.vo.LargeStuffVO;
import com.yutoudev.irrigation.module.system.api.dept.DeptApi;
import com.yutoudev.irrigation.oa.api.StuffApi;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/irr/large-stuff")
@Validated
public class LargeStuffController {

    @Value("${stuff.enabled:true}")
    private Boolean stuffEnabled;

    @Resource
    private DeptApi deptApi;

    @Resource
    private StuffApi stuffApi;

    /**
     * 获取在职员工统计与考勤统计
     */
    @GetMapping("/getLargeStuff")
    public CommonResult<LargeStuffVO> getLargeStuff() {
        Integer employedNumber =deptApi.getEmployedNumber();
        if (!stuffEnabled) {
            return success(new LargeStuffVO(employedNumber, employedNumber));
        }
        return success(new LargeStuffVO(employedNumber, stuffApi.getClockNumber()));
    }
}
