<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.inletanalysisdetail.InletAnalysisDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->


    <select id="sumRainfallGroupByCatchmentIdAndDay" resultType="java.lang.Double">
        SELECT SUM(rainfall) from irr_inlet_analysis_detail WHERE catchment_id=#{catchmentId} AND realtime between #{startTime} AND #{endTime}
    </select>
    <select id="sumRainfallVolumeGroupByCatchmentIdAndDay" resultType="java.lang.Double">
        SELECT SUM(rainfall_volume) from irr_inlet_analysis_detail WHERE catchment_id=#{catchmentId} AND realtime between #{startTime} AND #{endTime}
    </select>


    <select id="getShortTermRainfall" resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.ShortTermRainfallRespVO">
        SELECT
            hours AS `hour`,
            ROUND(COALESCE(SUM( CASE WHEN realtime >= NOW() - INTERVAL hours HOUR THEN rainfall_volume ELSE 0 END ), 0), 1) AS rainfall
        FROM
            irr_inlet_analysis_detail,
            (
                SELECT
                    1 AS hours UNION ALL
                SELECT
                    3 UNION ALL
                SELECT
                    6 UNION ALL
                SELECT
                    12 UNION ALL
                SELECT
                    24 UNION ALL
                SELECT
                    48 UNION ALL
                SELECT
                    72
            ) AS time_ranges
        WHERE
            realtime >= NOW() - INTERVAL 72 HOUR
          AND swhs_id = #{swhsId}
        GROUP BY
            hours
        ORDER BY
            hours
    </select>
</mapper>
