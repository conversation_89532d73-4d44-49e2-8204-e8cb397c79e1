package com.yutoudev.irrigation.irr.controller.admin.rainfallprocess;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.rainfallprocess.vo.site.*;
import com.yutoudev.irrigation.irr.convert.rainfallprocess.RainfallProcessSiteConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.RainfallProcessSiteDO;
import com.yutoudev.irrigation.irr.service.rainfallprocess.RainfallProcessSiteService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 雨量站降雨过程
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/rainfall-process-site")
@Validated
public class RainfallProcessSiteController {

    private static final String MODULE_NAME = "雨量站降雨过程";

    @Resource
    private RainfallProcessSiteService<RainfallProcessSiteDO> rainfallProcessSiteService;

    /**
     * 创建雨量站降雨过程
     *
     * @param createReqVO RainfallProcessSiteCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody RainfallProcessSiteCreateReqVO createReqVO) {
        return success(rainfallProcessSiteService.create(createReqVO));
    }

    /**
     * 批量创建雨量站降雨过程
     *
     * @param lists RainfallProcessSiteCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<RainfallProcessSiteCreateReqVO> lists) {
        return success(rainfallProcessSiteService.createBatch(lists));
    }

    /**
     * 更新雨量站降雨过程
     *
     * @param updateReqVO RainfallProcessSiteUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody RainfallProcessSiteUpdateReqVO updateReqVO) {
        rainfallProcessSiteService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新雨量站降雨过程
     *
     * @param lists 批量更新列表 RainfallProcessSiteUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<RainfallProcessSiteUpdateReqVO> lists) {
        return success(rainfallProcessSiteService.updateBatch(lists));
    }

    /**
     * 删除雨量站降雨过程
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        rainfallProcessSiteService.delete(id);
        return success(true);
    }

    /**
     * 批量删除雨量站降雨过程
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(rainfallProcessSiteService.deleteBatch(ids));
    }

    /**
     * 获得雨量站降雨过程详情
     *
     * @param id 编号 Long
     * @return CommonResult<RainfallProcessSiteDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasAnyPermissions('irr:rainfall-process-site:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<RainfallProcessSiteDetailRespVO> get(@RequestParam("id") Long id) {
        RainfallProcessSiteDO rainfallProcessSite = rainfallProcessSiteService.get(id);
        return success(RainfallProcessSiteConvert.INSTANCE.convertDetail(rainfallProcessSite));
    }

    /**
     * 雨量站降雨过程列表
     *
     * @param queryReqVO 查询条件 RainfallProcessSiteQueryReqVO
     * @return CommonResult<List < RainfallProcessSiteRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasAnyPermissions('irr:rainfall-process-site:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<RainfallProcessSiteRespVO>> getList(@RequestQueryParam RainfallProcessSiteQueryReqVO queryReqVO) {
        List<RainfallProcessSiteDO> list = rainfallProcessSiteService.getList(queryReqVO);
        return success(RainfallProcessSiteConvert.INSTANCE.convertList(list));
    }

    /**
     * 雨量站降雨过程分页
     *
     * @param pageVO 查询条件 RainfallProcessSitePageReqVO
     * @return CommonResult<PageResult < RainfallProcessSiteRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasAnyPermissions('irr:rainfall-process-site:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<RainfallProcessSiteRespVO>> page(@RequestQueryParam RainfallProcessSitePageReqVO pageVO) {
        PageResult<RainfallProcessSiteDO> pageResult = rainfallProcessSiteService.page(pageVO);
        return success(RainfallProcessSiteConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出雨量站降雨过程Excel
     *
     * @param queryReqVO 查询条件 RainfallProcessSiteExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam RainfallProcessSiteExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<RainfallProcessSiteDO> list = rainfallProcessSiteService.getList(queryReqVO);
        // 导出 Excel
        List<RainfallProcessSiteExcelVO> datas = RainfallProcessSiteConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "雨量站降雨过程", "xlsx"), queryReqVO.getExportSheetName(),
                RainfallProcessSiteExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入雨量站降雨过程模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "雨量站降雨过程-导入模版.xls", "sheet1", RainfallProcessSiteExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入雨量站降雨过程Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-site:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<RainfallProcessSiteExcelVO> list = ExcelUtils.read(file, RainfallProcessSiteExcelVO.class);
        return success(rainfallProcessSiteService.importExcel(list, isUpdate));
    }
}