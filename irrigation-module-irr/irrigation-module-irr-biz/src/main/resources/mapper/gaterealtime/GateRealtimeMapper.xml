<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.gaterealtime.GateRealtimeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="centralIdAndDevId" resultType="java.lang.Long">
        SELECT id FROM irr_gate_realtime WHERE central_id = #{centralId} AND dev_id = #{devId}
    </select>

    <select id="selectByCentralIdAndDevId"
            resultType="com.yutoudev.irrigation.irr.dal.dataobject.gaterealtime.GateRealtimeDO">
        SELECT * FROM irr_gate_realtime WHERE central_id = #{centralId} AND dev_id = #{devId}
    </select>
    <select id="listGateSiteInfo"
            resultType="com.yutoudev.irrigation.irr.controller.app.equip.vo.GateSiteRespVO">
        SELECT
        equip.id AS id,
          gr.v1 AS v1,
          gr.v2 AS v2,
          gr.v3 AS v3,
          gr.v4 AS v4,
          gr.v5 AS v5,
          gr.v6 AS v6,
          gr.v7 AS v7,
          gr.v8 AS v8,
          gr.v9 AS v9,
          gr.v10 AS v10,
          gr.v11 AS v11,
          gr.v12 AS v12,
          gr.v13 AS v13,
          gr.v14 AS v14,
          gr.`status` AS `status`,
          gr.exec_res AS exec_res,
          gr.update_time AS update_time,
          chan.id AS chan_id,
          chan.chan_name AS chan_name,
          chan.parent_id AS parent_id,
          chan.parent_tree_id AS parent_tree_id,
          chan.swhs_id AS swhs_id,
          chan.max_warning_value AS max_warning_value,
          chan.chan_type AS chan_type,
          chan.mlg_num AS chan_mlg_num,
          chan.sort AS chan_sort,
          equip.id as equip_id,
          equip.st_name AS st_name,
          equip.dev_id AS dev_id,
          equip.central_id AS central_id,
          equip.dev_type AS dev_type,
          equip.pic_url AS pic_url,
          equip.`online` AS `online`,
          equip.take_photo AS take_photo,
          equip.protocol AS protocol,
          equip.sort AS equip_sort,
          equip.dpds_num AS equip_mlg_num,
          gateFile.url AS take_photo_url,
          warnInfo.id AS warn_id,
          warnInfo.warn_content AS warn_content,
          warnLevel.warn_color AS warn_color,
          warnLevel.warn_level AS warn_level,
          warnLevel.warn_level_name AS warn_level_name,
          warnInfo.create_time AS warn_time
        FROM
          irr_equip_base AS equip
          LEFT JOIN irr_gate_realtime as gr ON gr.equip_id = equip.id AND gr.central_id = equip.central_id AND gr.dev_id = equip.dev_id
          LEFT JOIN irr_chan_equip_query AS chanEquip ON chanEquip.equip_id = equip.id
          LEFT JOIN irr_chan_base AS chan ON chan.id = chanEquip.chan_id
          LEFT JOIN (
          SELECT
            t.id,
            t.create_time,
            t.equip_id,
                warn_level_id,
            t.warn_content
          FROM
            ew_warn_info t
            INNER JOIN (
            SELECT
              equip_id,
              MAX( create_time ) AS max_time
            FROM
              ew_warn_info
            WHERE
                  equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1)
              and deleted = 0
              AND create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
            GROUP BY
              equip_id
            ) m ON t.equip_id = m.equip_id
            AND t.create_time = m.max_time
          ) AS warnInfo ON warnInfo.equip_id = equip.id
          LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
          LEFT JOIN ( SELECT
                    id,
                    create_time,
                    dev_id,
                    central_id,
                    url
                FROM
                    ( SELECT
                      t.*,
                      ROW_NUMBER() OVER ( PARTITION BY t.central_id,t.dev_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
                    FROM
                      irr_gate_file t
                    WHERE
                      t.deleted = 0
                    ) AS sub
                WHERE
                    rn = 1) AS gateFile ON gateFile.central_id = equip.central_id AND gateFile.dev_id = equip.dev_id
        WHERE
          equip.deleted = 0 AND equip.st_type = 7
    </select>
</mapper>
