package com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo;

import lombok.*;
import java.util.*;
import java.time.*;
    import java.math.BigDecimal;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationplanyear.AllocationPlanYearDO;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;


/**
 *
 * 配水调度年计划list查询RequestVO
 * @description 管理后台-配水调度年计划list查询RequestVO，参数和 AllocationPlanYearPageReqVO 是一致的
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@Data
public class AllocationPlanYearQueryReqVO extends QueryCriteria<AllocationPlanYearDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 计划名称
     * 
     */
    private String name;

    /**
     * 用水用户ID
     * 
     */
    private Long userId;

    /**
     * 灌溉片区ID
     * 
     */
    private Long zoneId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 计划供水
     * @mock （m³）
     */
    private BigDecimal supplyWater;

    /**
     * 计划时间
     * @mock （yyyy）
     */
    private String planTime;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 创建人
     * 
     */
    private String creator;

    /**
     * 创建时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    /**
     * 修改人
     * 
     */
    private String updater;

    /**
     * 修改时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 删除标志
     * 
     */
    private Boolean deleted;

    /**
     * 租户编号
     * 
     */
    private Long tenantId;

}
