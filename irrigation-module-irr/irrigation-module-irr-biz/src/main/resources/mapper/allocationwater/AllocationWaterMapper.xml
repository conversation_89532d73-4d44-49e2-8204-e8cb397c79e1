<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.allocationwater.AllocationWaterMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectInletInversionModel"
            parameterType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelQueryReqVo"
            resultType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelRespVo">
        SELECT w.swhs_id,
               w.swhs_name,
               SUM(w.plan_water) plan_water,
               SUM(s.water)      water
        FROM irr_allocation_water w
                 LEFT JOIN irr_allocation_water_scheme s ON w.id = s.allocation_water_id
        WHERE s.swhs_id IS NOT NULL
          AND ((w.start_time BETWEEN #{beginDate} AND #{endDate} AND w.end_time &gt;= #{endDate})
            OR (w.end_time BETWEEN #{beginDate} AND #{endDate} AND w.start_time &lt;= #{beginDate})
            OR (w.start_time &lt;= #{beginDate} AND w.end_time &gt;= #{endDate})
            OR (w.start_time &gt;= #{beginDate} AND w.end_time &lt;= #{endDate}))
        GROUP BY s.swhs_id
        ORDER BY s.swhs_id ASC
    </select>

    <select id="selectInletInversionModelBySwhsId"
            parameterType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelQueryReqVo"
            resultType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelRespVo">
        SELECT s.chan_id,
               s.chan_name,
               SUM(w.plan_water) plan_water,
               SUM(s.water)      water
        FROM irr_allocation_water w
                 LEFT JOIN irr_allocation_water_scheme s ON w.id = s.allocation_water_id
        WHERE w.swhs_id = #{swhsId}
          AND s.chan_id IS NOT NULL
          AND ((w.start_time BETWEEN #{beginDate} AND #{endDate} AND w.end_time &gt;= #{endDate})
            OR (w.end_time BETWEEN #{beginDate} AND #{endDate} AND w.start_time &lt;= #{beginDate})
            OR (w.start_time &lt;= #{beginDate} AND w.end_time &gt;= #{endDate})
            OR (w.start_time &gt;= #{beginDate} AND w.end_time &lt;= #{endDate}))
        GROUP BY s.chan_id
        ORDER BY s.chan_id ASC
    </select>

    <select id="selectInletInversionModelByChanId"
            parameterType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelQueryReqVo"
            resultType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelRespVo">
        SELECT
        w.user_name,
        w.use_type,
        w.dept_name,
        w.town_name,
        w.zone_name,
        w.plan_water,
        w.water,
        w.start_time,
        w.end_time
        FROM
        irr_allocation_water w
        LEFT JOIN irr_allocation_water_scheme s ON w.id = s.allocation_water_id
        WHERE
        w.swhs_id = #{swhsId} AND s.chan_id IS NOT NULL
        <if test="chanId != 0">
            and s.chan_id = #{chanId}
        </if>
        AND (( w.start_time BETWEEN #{beginDate} AND #{endDate} AND w.end_time &gt;= #{endDate} )
        OR ( w.end_time BETWEEN #{beginDate} AND #{endDate} AND w.start_time &lt;= #{beginDate} )
        OR ( w.start_time &lt;= #{beginDate} AND w.end_time &gt;= #{endDate} )
        OR ( w.start_time &gt;= #{beginDate} AND w.end_time &lt;= #{endDate} ))
        GROUP BY
        w.id ORDER BY w.start_time ASC
    </select>

    <select id="statisticsAllocationWaterByChan"
            resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterStatisticsRespVO">
        (SELECT icb.id,iaw.chan_id, icb.chan_name, icb.parent_id, sum(iaw.plan_water) as plan_water, sum(iaw.water) as water
        FROM irr_allocation_water as iaw
        INNER JOIN irr_chan_base icb ON icb.id = iaw.chan_id
        WHERE iaw.deleted = 0
        <if test="ew.sqlSegment != ''">
            and ${ew.sqlSegment}
        </if>
        GROUP BY iaw.chan_id) ORDER BY chan_id
    </select>
    <select id="statisticsAllocationWaterByUser"
            resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterStatisticsRespVO">
        (SELECT chan_id, chan_name, user_id, user_name, sum(plan_water) as plan_water, sum(water) as water
        FROM irr_allocation_water
        WHERE deleted = 0
        <if test="ew.sqlSegment != ''">
            and ${ew.sqlSegment}
        </if>
        GROUP BY chan_id, user_id) ORDER BY chan_id, user_id
    </select>
    <select id="statisticsAllocationWaterByDay"
            resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterStatisticsRespVO">
        (SELECT date_format(end_time, '%Y-%m-%d') as statistics_date, sum(plan_water) as plan_water, sum(water) as water
         FROM irr_allocation_water
        WHERE deleted = 0
        <if test="ew.sqlSegment != ''">
            and ${ew.sqlSegment}
        </if>
         GROUP BY date_format(end_time, '%Y-%m-%d')) ORDER BY statistics_date
    </select>
    <select id="statisticsAllocationWaterByMonth"
            resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterStatisticsRespVO">
        (SELECT date_format(end_time, '%Y-%m') as statistics_month, sum(plan_water) as plan_water, sum(water) as water
        FROM irr_allocation_water
        WHERE deleted = 0
        <if test="ew.sqlSegment != ''">
            and ${ew.sqlSegment}
        </if>
        GROUP BY date_format(end_time, '%Y-%m')) ORDER BY statistics_month
    </select>

    <select id="sumPlanWater" resultType="java.lang.Double">
        SELECT SUM(plan_water) plan_water FROM irr_allocation_water WHERE start_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="sumStatistics" resultType="com.yutoudev.irrigation.irr.controller.admin.waterdemandmodel.vo.WaterDemandModelRespVo">
        SELECT COUNT(DISTINCT user_id) user_count, SUM(plan_water) plan_water ,SUM(water) water FROM irr_allocation_water
    </select>

    <select id="selectGropyByUserId" resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterSchedulingRespVO">
        SELECT
            w.user_id id,
            w.user_name `name`,
            eb.dpds_num,
            w.water,
            w.flow_rate,
            s.gate_height,
            w.start_time gateTime
        FROM
            ( SELECT * FROM `irr_allocation_water` WHERE start_time > NOW()  ORDER BY start_time ASC ) w
                LEFT JOIN irr_allocation_water_scheme s ON w.chan_id = s.chan_id
                LEFT JOIN irr_equip_base eb ON s.equip_id = eb.id
        GROUP BY
            w.user_id
        ORDER BY
            w.start_time ASC
    </select>

    <select id="selectByUserId" resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterSchedulingRespVO">
        SELECT
            w.user_id id,
            w.user_name `name`,
            eb.dpds_num,
            w.water,
            w.flow_rate,
            s.gate_height,
            w.start_time gateTime
        FROM
            ( SELECT * FROM `irr_allocation_water` WHERE start_time > NOW() AND user_id = #{userId} ORDER BY start_time ASC ) w
                LEFT JOIN irr_allocation_water_scheme s ON w.chan_id = s.chan_id
                LEFT JOIN irr_equip_base eb ON s.equip_id = eb.id
        GROUP BY
            w.id
        ORDER BY
            w.start_time ASC
    </select>


    <select id="selectWaterSupplyUseAnalysisTop" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterSupplyUseAnalysisTopRespVO">
        SELECT user_name,SUM(water) use_water FROM `irr_allocation_water` WHERE DATE_FORMAT(start_time, #{timeFormat}) = #{time} GROUP BY user_id ORDER BY use_water DESC
    </select>

    <select id="selectWaterSupplyUseAnalysisPieByUseType" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterSupplyUseAnalysisPieRespVO">
        SELECT use_type name,SUM(water) use_water FROM `irr_allocation_water` WHERE DATE_FORMAT(start_time, #{timeFormat}) = #{time} GROUP BY use_type ORDER BY use_water DESC
    </select>

    <select id="selectWaterSupplyUseAnalysisPieByTown" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterSupplyUseAnalysisPieRespVO">
        SELECT town_name name,SUM(water) use_water FROM `irr_allocation_water` WHERE DATE_FORMAT(start_time, #{timeFormat}) = #{time} GROUP BY town_id ORDER BY use_water DESC
    </select>

    <select id="selectCropIrrigations" resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterCropIrrigationRespVO">
        SELECT
            awi.crop_id,
            awi.crop_name,
            awi.crop_area,
            awi.quota,
            SUM( awi.plan_water ) plan_water,
            SUM( awi.water ) water,
            SUM( awi.monitor_water ) monitor_water,
            aw.start_time start_time
        FROM
            irr_allocation_water_item awi
                LEFT JOIN irr_allocation_water aw ON aw.id = awi.water_id
        WHERE
            aw.user_id = #{userId} AND aw.start_time >= NOW() - INTERVAL 300 DAY
        GROUP BY
            awi.crop_id,
            awi.crop_area
    </select>


    <select id="getExecutingUseWater" resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.UseTypeWaterVO">
        SELECT
            sdd.value AS id,
            sdd.label AS name,
            IFNULL(ROUND(SUM(iaw.water)/10000, 2), 0) AS accumulate
        FROM
            system_dict_data sdd
                LEFT JOIN
            irr_allocation_water iaw
            ON
                sdd.value = iaw.use_type
                    AND iaw.end_time &gt; NOW()
        WHERE
            sdd.dict_type = 'irr_user_use_type'
        GROUP BY
            sdd.label, sdd.value
        ORDER BY
            sdd.value ASC
    </select>

    <select id="getActualUseWater" resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.UseTypeWaterVO">
        SELECT
            sdd.value AS id,
            sdd.label AS name,
            IFNULL(ROUND(SUM(iaw.water)/10000, 2), 0) AS accumulate
        FROM
            system_dict_data sdd
        LEFT JOIN
            irr_allocation_water iaw
        ON
            sdd.value = iaw.use_type
            AND iaw.end_time &lt; NOW() AND DATE_FORMAT( iaw.end_time, '%Y' ) = YEAR(NOW())
        WHERE
            sdd.dict_type = 'irr_user_use_type'
        GROUP BY
            sdd.label, sdd.value
        ORDER BY
            sdd.value ASC
    </select>


    <select id="getUseWater" resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.UseWaterRespVO">
        SELECT
            ROUND(t.current_year_water/10000 , 2) `year`,
            ROUND(t.current_month_water/10000, 2) `month`,
            ROUND(t.current_day_water/10000, 2) `day`,
            ROUND(
                    CASE

                        WHEN ( t.current_month_water - t.last_year_month_water ) = 0 THEN
                            1
                        WHEN t.last_year_month_water = 0 THEN
                            1 ELSE ( t.current_month_water - t.last_year_month_water ) / t.last_year_month_water
                        END,
                    4
            ) * 100 AS monthYOY,
            ROUND(
                    CASE

                        WHEN ( t.current_year_water - t.last_year_month_water ) = 0 THEN
                            1
                        WHEN t.last_year_month_water = 0 THEN
                            1 ELSE ( t.current_year_water - t.last_year_month_water ) / t.last_year_month_water
                        END,
                    4
            ) * 100 AS yearYOY
        FROM
            (
                SELECT
                    SUM( CASE WHEN DATE_FORMAT( end_time, '%Y' ) = DATE_FORMAT( NOW(), '%Y' ) THEN water ELSE 0 END ) AS current_year_water,
                    SUM( CASE WHEN DATE_FORMAT( end_time, '%Y-%m' ) = DATE_FORMAT( NOW(), '%Y-%m' ) THEN water ELSE 0 END ) AS current_month_water,
                    SUM( CASE WHEN DATE_FORMAT( end_time, '%Y-%m-%d' ) = DATE_FORMAT( NOW(), '%Y-%m-%d' ) THEN water ELSE 0 END ) AS current_day_water,-- 去年的数据
                    SUM( CASE WHEN DATE_FORMAT( end_time, '%Y' ) = DATE_FORMAT( DATE_SUB( NOW(), INTERVAL 1 YEAR ), '%Y' ) THEN water ELSE 0 END ) AS last_year_water,
                    SUM( CASE WHEN DATE_FORMAT( end_time, '%Y-%m' ) = DATE_FORMAT( DATE_SUB( NOW(), INTERVAL 1 YEAR ), '%Y-%m' ) THEN water ELSE 0 END ) AS last_year_month_water
                FROM
                    irr_allocation_water
                WHERE
                    (
                        DATE_FORMAT( end_time, '%Y' ) = DATE_FORMAT( NOW(), '%Y' )
                            OR (
                            DATE_FORMAT( end_time, '%Y-%m-%d' ) &gt;= DATE_FORMAT( DATE_SUB( NOW(), INTERVAL 1 YEAR ), '%Y-01-01' )
                                AND DATE_FORMAT( end_time, '%Y-%m-%d' ) &lt;= DATE_FORMAT( DATE_SUB( NOW(), INTERVAL 1 YEAR ), '%Y-%m-%d' )
                            )
                        ))t
    </select>

    <select id="getWaterEfficiency" resultType="com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterEfficiencyRespVO">
        SELECT
            aw.chan_id,
            aw.chan_name,
            SUM( awi.water ) water,
            SUM( awi.quota ) quota
        FROM
            irr_allocation_water aw
                LEFT JOIN irr_allocation_water_item awi ON awi.water_id = aw.id
        WHERE
            DATE_FORMAT( aw.end_time, '%Y' ) = DATE_FORMAT( NOW(), '%Y' )
        GROUP BY
            aw.chan_id
    </select>

    <select id="getWaterPlan" resultType="com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterPlanRespVO">
        SELECT
            main.chan_id,
            main.chan_name,
            main.water,
            main.quota,
            hist.total_water,
            (main.quota - hist.total_water - main.water) as remaining
        FROM (
                 SELECT
                     t.chan_id,
                     t.chan_name,
                     SUM(t.water) AS water,
                     SUM(t.quota) AS quota
                 FROM (
                          SELECT
                              aw.chan_id,
                              aw.chan_name,
                              aw.water,
                              IFNULL(SUM(awi.quota), 0) AS quota
                          FROM
                              `irr_allocation_water` aw
                                  LEFT JOIN irr_allocation_water_item awi ON awi.water_id = aw.id
                          WHERE aw.end_time &gt;= NOW()
                            <if test="planIds != null">
                                AND aw.plan_id in
                                <foreach collection="planIds" item="planId" open="(" separator="," close=")">
                                    #{planId}
                                </foreach>
                            </if>
                          GROUP BY
                              aw.id
                      ) t
                 GROUP BY
                     t.chan_id, t.chan_name
             ) main
                 LEFT JOIN (
            SELECT
                aw.chan_id,
                IFNULL(SUM(awi.water), 0) AS total_water
            FROM
                `irr_allocation_water` aw
                    LEFT JOIN irr_allocation_water_item awi ON awi.water_id = aw.id
            WHERE aw.end_time &lt;= NOW() AND aw.start_time &gt;= NOW() - INTERVAL 300 DAY
            GROUP BY
                aw.chan_id
        ) hist ON main.chan_id = hist.chan_id
    </select>

    <select id="getWaterPlanChanInfo" resultType="com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterPlanChanInfoRespVO">
        SELECT
            t.chan_id chan_id,
            t.chan_name chan_name,
            t.water water
        FROM
            (
                SELECT
                    wdi.chan_id,
                    wdi.chan_name,
                    SUM( wdi.gross ) water
                FROM
                    `irr_allocation_water` aw
                        LEFT JOIN irr_water_diversion_inversion wdi ON wdi.plan_id = aw.plan_id
                WHERE aw.end_time &gt;= NOW() and wdi.type=0 and wdi.is_discharge_gate=0
                GROUP BY
                    wdi.chan_id
            ) t
        ORDER BY
            t.chan_id;
    </select>

</mapper>
