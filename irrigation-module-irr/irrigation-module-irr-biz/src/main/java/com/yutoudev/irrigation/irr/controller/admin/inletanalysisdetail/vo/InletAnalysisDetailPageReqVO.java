package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisdetail.InletAnalysisDetailDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 *
 * 来水分析明细分页RequestVO
 * @description 管理后台-来水分析明细分页RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:37
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisDetailPageReqVO extends PageCriteria<InletAnalysisDetailDO> {

    /**
     * 集水面积ID
     *
     */
    private Long catchmentId;

    /**
     * 水源ID
     *
     */
    private Long swhsId;

    /**
     * 水渠ID
     *
     */
    private Long chanId;

    /**
     * 统计时间
     */
    private LocalDateTime realtime;

    /**
     * 降雨量
     *
     */
    private Double rainfall;

    /**
     * 蒸发量
     *
     */
    private Double evaporation;

    /**
     * 渗透值
     *
     */
    private Double penetration;

    /**
     * 径流值
     *
     */
    private Double runoff;

    /**
     * 径流时间
     * @mock （分钟）
     */
    private Integer runoffTime;

    /**
     * 创建时间
     *
     */
    private LocalDateTime createTime;

}
