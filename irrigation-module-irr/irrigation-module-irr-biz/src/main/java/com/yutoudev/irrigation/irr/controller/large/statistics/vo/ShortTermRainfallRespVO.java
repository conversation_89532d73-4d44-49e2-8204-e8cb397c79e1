package com.yutoudev.irrigation.irr.controller.large.statistics.vo;

import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * 短时降雨统计ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-短时降雨统计ResponseVO
 * @time 2025-03-11
 */
@Data
@ToString(callSuper = true)
public class ShortTermRainfallRespVO {

    /**
     * 小时：1，3，6，12，24，48，72
     */
    private Integer hour;

    /**
     * 累计降雨量
     */
    private Double rainfall;


}
