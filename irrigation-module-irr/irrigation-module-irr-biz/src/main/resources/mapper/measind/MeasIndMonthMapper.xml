<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndMonthMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectByTime" resultType="com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndMonthDO">
        SELECT * FROM irr_meas_ind_month WHERE central_id = #{centralId} AND dev_id = #{devId} AND month=#{month} ORDER BY update_time DESC LIMIT 1
    </select>

    <select id="sumRainfallVolume" resultType="java.lang.Double">
        SELECT SUM(rainfall_volume) rainfall_volume FROM irr_meas_ind_month WHERE equip_id=#{equipId} AND `month` BETWEEN #{startTime} AND #{endTime}
    </select>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into irr_meas_ind_month(id, equip_id, central_id, dev_id, month, rainfall_volume, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.equipId}, #{entity.centralId}, #{entity.devId}, #{entity.month}, #{entity.rainfallVolume}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        equip_id = values(equip_id),
        central_id = values(central_id),
        dev_id = values(dev_id),
        month = values(month),
        rainfall_volume = values(rainfall_volume),
        update_time = values(update_time)
    </insert>
</mapper>
