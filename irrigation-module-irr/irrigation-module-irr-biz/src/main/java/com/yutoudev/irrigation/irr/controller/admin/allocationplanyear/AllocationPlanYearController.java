package com.yutoudev.irrigation.irr.controller.admin.allocationplanyear;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.constraints.*;
import javax.validation.*;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;


import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationplanyear.AllocationPlanYearDO;
import com.yutoudev.irrigation.irr.convert.allocationplanyear.AllocationPlanYearConvert;
import com.yutoudev.irrigation.irr.service.allocationplanyear.AllocationPlanYearService;


/**
 *
 * 配水调度年计划
 * @description 管理后台-配水调度年计划controller
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@RestController
@RequestMapping("/irr/allocation-plan-year")
@Validated
public class AllocationPlanYearController {

    private static final String MODULE_NAME = "配水调度年计划";

    @Resource
    private AllocationPlanYearService<AllocationPlanYearDO> allocationPlanYearService;

    /**
     * 创建配水调度年计划
     * @description 单个对象保存
     * @param createReqVO AllocationPlanYearCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody AllocationPlanYearCreateReqVO createReqVO) {
        return success(allocationPlanYearService.create(createReqVO));
    }

    /**
     * 批量创建配水调度年计划
     * @description 多个对象保存
     * @param lists  AllocationPlanYearCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<AllocationPlanYearCreateReqVO> lists) {
        return success(allocationPlanYearService.createBatch(lists));
    }

    /**
     * 更新配水调度年计划
     * @description 单个对象修改
     * @param updateReqVO AllocationPlanYearUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody AllocationPlanYearUpdateReqVO updateReqVO) {
        allocationPlanYearService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新配水调度年计划
     * @description 批量更新
     * @param lists 批量更新列表 AllocationPlanYearUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<AllocationPlanYearUpdateReqVO> lists) {
        return success(allocationPlanYearService.updateBatch(lists));
    }

    /**
     * 删除配水调度年计划
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        allocationPlanYearService.delete(id);
        return success(true);
    }

    /**
     * 批量删除配水调度年计划
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(allocationPlanYearService.deleteBatch(ids));
    }

    /**
     * 获得配水调度年计划详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<AllocationPlanYearDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<AllocationPlanYearDetailRespVO> get(@RequestParam("id") Long id) {
        AllocationPlanYearDO allocationPlanYear = allocationPlanYearService.get(id);
        return success(AllocationPlanYearConvert.INSTANCE.convertDetail(allocationPlanYear));
    }

    /**
     * 配水调度年计划列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 AllocationPlanYearQueryReqVO
     * @return CommonResult<List<AllocationPlanYearRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<AllocationPlanYearRespVO>> getList(@RequestQueryParam AllocationPlanYearQueryReqVO queryReqVO) {
        List<AllocationPlanYearDO> list = allocationPlanYearService.getList(queryReqVO);
        return success(AllocationPlanYearConvert.INSTANCE.convertList(list));
    }

    /**
     * 配水调度年计划分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 AllocationPlanYearPageReqVO
     * @return CommonResult<PageResult<AllocationPlanYearRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<AllocationPlanYearRespVO>> page(@RequestQueryParam AllocationPlanYearPageReqVO pageVO) {
        PageResult<AllocationPlanYearDO> pageResult = allocationPlanYearService.page(pageVO);
        return success(AllocationPlanYearConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出配水调度年计划Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 AllocationPlanYearExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam AllocationPlanYearExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<AllocationPlanYearDO> list = allocationPlanYearService.getList(queryReqVO);
        // 导出 Excel
        List<AllocationPlanYearExcelVO> datas = AllocationPlanYearConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "配水调度年计划", "xlsx"), queryReqVO.getExportSheetName(),
                                                    AllocationPlanYearExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入配水调度年计划模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "配水调度年计划-导入模版.xls", "sheet1", AllocationPlanYearExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入配水调度年计划Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-year:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<AllocationPlanYearExcelVO> list = ExcelUtils.read(file, AllocationPlanYearExcelVO.class);
        return success(allocationPlanYearService.importExcel(list, isUpdate));
    }
}