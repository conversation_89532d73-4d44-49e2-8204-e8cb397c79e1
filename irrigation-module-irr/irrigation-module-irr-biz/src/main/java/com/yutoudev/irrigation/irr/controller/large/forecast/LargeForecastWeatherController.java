package com.yutoudev.irrigation.irr.controller.large.forecast;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.forecast.vo.weather.ForecastWeatherPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.forecast.vo.weather.ForecastWeatherRespVO;
import com.yutoudev.irrigation.irr.convert.forecast.ForecastWeatherConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.forecast.ForecastWeatherDO;
import com.yutoudev.irrigation.irr.service.forecast.ForecastWeatherService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 天气预报
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/forecast-weather")
@Validated
public class LargeForecastWeatherController {

    private static final String MODULE_NAME = "天气预报";

    @Resource
    private ForecastWeatherService<ForecastWeatherDO> forecastWeatherService;

    /**
     * 天气预报分页
     *
     * @param pageVO 查询条件 ForecastWeatherPageReqVO
     * @return CommonResult<PageResult < ForecastWeatherRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<ForecastWeatherRespVO>> page(@RequestQueryParam ForecastWeatherPageReqVO pageVO) {
        PageResult<ForecastWeatherDO> pageResult = forecastWeatherService.page(pageVO);
        return success(ForecastWeatherConvert.INSTANCE.convertPage(pageResult));
    }

}