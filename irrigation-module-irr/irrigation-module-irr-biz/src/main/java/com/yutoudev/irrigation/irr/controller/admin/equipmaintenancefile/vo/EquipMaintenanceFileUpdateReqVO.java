package com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;


/**
 * 设备维护附件更新RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护附件更新RequestVO
 * @time 2024-10-27 10:33:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EquipMaintenanceFileUpdateReqVO extends EquipMaintenanceFileBaseVO {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}