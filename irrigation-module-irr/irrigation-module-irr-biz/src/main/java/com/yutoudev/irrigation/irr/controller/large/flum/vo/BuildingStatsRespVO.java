package com.yutoudev.irrigation.irr.controller.large.flum.vo;

import lombok.Data;

/**
 * 大屏 - 工程建设统计 Response VO
 * <AUTHOR>
 */
@Data
public class BuildingStatsRespVO {
    
    /**
     * 建筑物总数
     */
    private Integer totalCount;
    /**
     * 类型A的数量
     */
    private Integer typeACount;
    /**
     * 类型B的数量
     */
    private Integer typeBCount;
    /**
     * 类型C的数量
     */
    private Integer typeCCount;
    /**
     * 类型D的数量
     */
    private Integer typeDCount;
    /**
     * 现状建筑物完好率(%) （A+B）/总数
     */
    private Double intactRate;
}