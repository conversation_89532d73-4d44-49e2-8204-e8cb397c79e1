package com.yutoudev.irrigation.irr.controller.large.filldata;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.filldata.vo.reservoir.*;
import com.yutoudev.irrigation.irr.convert.filldata.FilldataReservoirConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.filldata.FilldataReservoirDO;
import com.yutoudev.irrigation.irr.service.filldata.FilldataReservoirService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 水源可用水量
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/filldata-reservoir")
@Validated
public class LargeFilldataReservoirController {

    private static final String MODULE_NAME = "数据上报-水源可用水量";

    @Resource
    private FilldataReservoirService<FilldataReservoirDO> filldataReservoirService;

    /**
     * 当前其它水源可用水量
     *
     * @return CommonResult<Double> 列表响应VO
     */
    @GetMapping("/now-total-water")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<Double> getNowTotalWater() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime eightClock = now.withHour(8).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime queryDate;
        if (now.isBefore(eightClock)) {
            queryDate = now.minusDays(1);
        } else {
            queryDate = now;
        }
        FilldataReservoirQueryReqVO queryReqVO = new FilldataReservoirQueryReqVO();
        queryReqVO.setMarkTime(queryDate.toLocalDate());
        List<FilldataReservoirDO> list = filldataReservoirService.getList(queryReqVO);
        double totalWaterValue = list.stream()
                .mapToDouble(FilldataReservoirDO::getWaterValue)
                .sum();
        return success(totalWaterValue);
    }

}