package com.yutoudev.irrigation.irr.controller.large.tunnel;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.tunnelbase.vo.TunnelBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.tunnelbase.vo.TunnelBaseRespVO;
import com.yutoudev.irrigation.irr.controller.large.flum.vo.BuildingStatsRespVO;
import com.yutoudev.irrigation.irr.convert.tunnelbase.TunnelBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.tunnelbase.TunnelBaseDO;
import com.yutoudev.irrigation.irr.enums.BuildingEngStatsEnum;
import com.yutoudev.irrigation.irr.service.tunnelbase.TunnelBaseService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 大屏 - 隧洞统计
 */
@RestController
@RequestMapping("/irr/tunnel-base")
@Validated
@Slf4j
public class LargeTunnelBaseController {

    private static final String MODULE_NAME = "大屏-隧洞统计";

    @Resource
    private TunnelBaseService<TunnelBaseDO> tunnelBaseService;

    /**
     * 获取隧洞工程建设统计
     */
    @GetMapping("/building-stats")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<BuildingStatsRespVO> statisticsBuilding() {
        return success(tunnelBaseService.statisticsBuilding());
    }

    /**
     * 重建隧洞分页
     *
     * @param pageVO 查询条件 TunnelBasePageReqVO
     */
    @GetMapping("/page-rebuilding")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<TunnelBaseRespVO>> pageRebuilding(@RequestQueryParam TunnelBasePageReqVO pageVO) {
        pageVO.setEngStat(BuildingEngStatsEnum.REBUILDING.getStatus());
        PageResult<TunnelBaseDO> pageResult = tunnelBaseService.page(pageVO);
        return success(TunnelBaseConvert.INSTANCE.convertPage(pageResult));
    }
}