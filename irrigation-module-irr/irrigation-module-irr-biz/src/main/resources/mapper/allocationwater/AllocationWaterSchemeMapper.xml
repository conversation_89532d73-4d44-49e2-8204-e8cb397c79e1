<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.allocationwater.AllocationWaterSchemeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectGropyByChanId" resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterSchedulingRespVO">
        SELECT
            s.chan_id id,
            s.chan_name `name`,
            eb.dpds_num,
            s.water,
            s.flow_rate,
            s.gate_height,
            s.start_time gateTime
        FROM
            ( SELECT * FROM `irr_allocation_water_scheme` WHERE start_time > NOW() AND chan_id IS NOT NULL ORDER BY start_time ASC ) s
                LEFT JOIN irr_equip_base eb ON s.equip_id = eb.id
        GROUP BY
            s.chan_id
        ORDER BY
            s.start_time ASC
    </select>

    <select id="selectByChanId" resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterSchedulingRespVO">
        SELECT
            s.chan_id id,
            s.chan_name `name`,
            s.gate_id,
            eb.dpds_num,
            s.water,
            s.flow_rate,
            s.gate_height,
            s.start_time gateTime
        FROM
            ( SELECT * FROM `irr_allocation_water_scheme` WHERE start_time > NOW() AND chan_id = #{chanId} ORDER BY start_time ASC ) s
                LEFT JOIN irr_equip_base eb ON s.equip_id = eb.id
        ORDER BY
            s.start_time ASC
    </select>

    <select id="selectWaterSupplyUseAnalysisChanLoss" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterSupplyUseAnalysisChanLossRespVO">
        WITH RECURSIVE calendar AS ( SELECT #{startErgodic} AS TIME UNION ALL SELECT TIME + 1 FROM calendar WHERE TIME &lt; #{ergodic} )
        SELECT c.TIME AS TIME,
        IFNULL( SUM( p.water - p.loss ), 0 ) use_water,
        IFNULL( SUM( p.loss ), 0 ) loss
        FROM
        calendar c
        LEFT JOIN ( SELECT * FROM irr_allocation_water_scheme WHERE DATE_FORMAT( start_time, #{timeFormat} ) = #{time}
        <if test="chanId != null">
            AND chan_id = #{chanId}
        </if>
        ) p ON DATE_FORMAT( p.start_time, #{groupBytimeFormat} ) = c.TIME
        GROUP BY
        c.TIME
        ORDER BY
        c.TIME
    </select>

    <select id="sumWaterBySwhsId" resultType="java.lang.Double">
        SELECT COALESCE(SUM(water), 0)  as water FROM `irr_allocation_water_scheme` WHERE DATE_FORMAT(start_time,'%Y-%m-%d') >= DATE_FORMAT(NOW(),'%Y-%m-%d') and swhs_id = #{swhsId}
    </select>

    <select id="getExecutingAllocationWaterId" resultType="java.lang.Long">
        SELECT COALESCE(
          (SELECT allocation_water_id FROM irr_allocation_water_scheme WHERE end_time > NOW() ORDER BY start_time ASC LIMIT 1),
          (SELECT allocation_water_id FROM irr_allocation_water_scheme ORDER BY id DESC LIMIT 1)
        ) AS final_id
    </select>
</mapper>
