<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndMinuteMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into irr_meas_ind_minute(
            id,
            equip_id,
            central_id,
            dev_id,
            report_time,
            rainfall,
            voltage,
            update_time
        )
        values
        <foreach collection="entities" item="entity" separator=",">
            (
             #{entity.id},
             #{entity.equipId},
             #{entity.centralId},
             #{entity.devId},
             #{entity.reportTime},
             #{entity.rainfall},
             #{entity.voltage},
             #{entity.updateTime}
            )
        </foreach>
        on duplicate key update
        equip_id = values(equip_id),
        central_id = values(central_id),
        dev_id = values(dev_id),
        report_time = values(report_time),
        rainfall = values(rainfall),
        voltage = values(voltage),
        update_time = values(update_time)
    </insert>

</mapper>
