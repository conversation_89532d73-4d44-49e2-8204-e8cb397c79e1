<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.history.HistoryDemandMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <insert id="autoSaveByYear">
        INSERT INTO irr_history_demand
        (name, code, frequency, swhs_id, swhs_name, total, avg_volume, mark_time, lister)
        SELECT
            DATE_FORMAT(start_time, '%Y年') AS name,
            DATE_FORMAT(start_time, '%Y') AS code,
            1 AS frequency,
            swhs_id,
            swhs_name,
            SUM(water) AS total,
            ROUND(AVG(water), 3) AS avg_volume,
            NOW() AS mark_time,
            'system' AS lister
        FROM
            irr_allocation_water
        WHERE
            DATE_FORMAT(start_time, '%Y') = #{time}
        GROUP BY
            swhs_id,
            DATE_FORMAT(start_time, '%Y')
        ON DUPLICATE KEY UPDATE
             name = VALUES(name),
             frequency = VALUES(frequency),
             swhs_name = VALUES(swhs_name),
             total = VALUES(total),
             avg_volume = VALUES(avg_volume),
             mark_time = VALUES(mark_time),
             lister = VALUES(lister)
    </insert>


    <insert id="autoSaveByMonth">
        INSERT INTO irr_history_demand
        (name, code, frequency, swhs_id, swhs_name, total, avg_volume, mark_time, lister)
        SELECT
            DATE_FORMAT(start_time, '%Y年%m月') AS name,
            DATE_FORMAT(start_time, '%Y%m') AS code,
            2 AS frequency,
            swhs_id,
            swhs_name,
            SUM(water) AS total,
            ROUND(AVG(water), 3) AS avg_volume,
            NOW() AS mark_time,
            'system' AS lister
        FROM
            irr_allocation_water
        WHERE
            DATE_FORMAT(start_time,  #{timeFormat} ) = #{time}
        GROUP BY
            swhs_id,
            DATE_FORMAT(start_time, '%Y%m')
        ON DUPLICATE KEY UPDATE
             name = VALUES(name),
             frequency = VALUES(frequency),
             swhs_name = VALUES(swhs_name),
             total = VALUES(total),
             avg_volume = VALUES(avg_volume),
             mark_time = VALUES(mark_time),
             lister = VALUES(lister)
    </insert>

    <update id="updateMaxAndMinInfo">
        UPDATE irr_history_demand hd
        JOIN (
            WITH ranked_data AS (
                SELECT
                    DATE_FORMAT(mark_day, #{codeFormat}) AS code,
                    demand_water,
                    mark_day,
                    swhs_id,
                    RANK() OVER (PARTITION BY DATE_FORMAT(mark_day, #{codeFormat}), swhs_id ORDER BY demand_water DESC) AS max_rank,
                    RANK() OVER (PARTITION BY DATE_FORMAT(mark_day, #{codeFormat}), swhs_id ORDER BY demand_water ASC) AS min_rank
                FROM
                    irr_history_demand_day
                WHERE
                    DATE_FORMAT(mark_day, #{timeFormat} ) = #{time}
            )
            SELECT
                code,
                swhs_id,
                MAX(CASE WHEN max_rank = 1 THEN demand_water END) AS max_volume,
                MAX(CASE WHEN max_rank = 1 THEN mark_day END) AS max_day,
                MAX(CASE WHEN min_rank = 1 THEN demand_water END) AS min_volume,
                MAX(CASE WHEN min_rank = 1 THEN mark_day END) AS min_day
            FROM
                ranked_data
            WHERE
                max_rank = 1 OR min_rank = 1
            GROUP BY
                code, swhs_id
        ) AS src ON hd.code = src.code AND hd.swhs_id = src.swhs_id
        SET
            hd.max_volume = src.max_volume,
            hd.max_day = src.max_day,
            hd.min_volume = src.min_volume,
            hd.min_day = src.min_day
    </update>

    <select id="getWaterDemandMonthRespVO" resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.WaterDemandMonthRespVO">
        WITH RECURSIVE months AS (
            SELECT 1 AS month
        UNION ALL
        SELECT month + 1 FROM months WHERE month &lt; #{month}
            ),
            year_data AS (
        SELECT
            m.month,
            CONCAT(YEAR(CURDATE())-1, LPAD(m.month, 2, '0')) AS prev_year_code,
            CONCAT(YEAR(CURDATE()), LPAD(m.month, 2, '0')) AS curr_year_code
        FROM months m
            ),
            water_data AS (
        SELECT
            yd.month,
            COALESCE(prev.total, 0) AS prev_year_total,
            COALESCE(curr.total, 0) AS curr_year_total,
            COALESCE(prev.swhs_id, curr.swhs_id) AS swhs_id,
            COALESCE(prev.swhs_name, curr.swhs_name) AS swhs_name
        FROM year_data yd
            LEFT JOIN irr_history_demand prev ON prev.code = yd.prev_year_code
            AND prev.frequency = 2
            AND prev.swhs_id = #{swhsId}
            LEFT JOIN irr_history_demand curr ON curr.code = yd.curr_year_code
            AND curr.frequency = 2
            AND curr.swhs_id = #{swhsId}
            AND (prev.swhs_id = curr.swhs_id OR (prev.swhs_id IS NULL AND curr.swhs_id IS NOT NULL))
            )
        SELECT
            wd.month AS month,
            wd.prev_year_total water,
            wd.curr_year_total thePreviousYearWater
        FROM water_data wd
        ORDER BY wd.month
    </select>

</mapper>
