package com.yutoudev.irrigation.irr.controller.admin.inletanalysismonth;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.constraints.*;
import javax.validation.*;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;


import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yutoudev.irrigation.irr.controller.admin.inletanalysismonth.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysismonth.InletAnalysisMonthDO;
import com.yutoudev.irrigation.irr.convert.inletanalysismonth.InletAnalysisMonthConvert;
import com.yutoudev.irrigation.irr.service.inletanalysismonth.InletAnalysisMonthService;


/**
 *
 * 月来水分析
 * @description 管理后台-月来水分析controller
 * <AUTHOR>
 * @time 2024-07-23 14:33:38
 *
 */
@RestController
@RequestMapping("/irr/inlet-analysis-month")
@Validated
public class InletAnalysisMonthController {

    private static final String MODULE_NAME = "月来水分析";

    @Resource
    private InletAnalysisMonthService<InletAnalysisMonthDO> inletAnalysisMonthService;

    /**
     * 创建月来水分析
     * @description 单个对象保存
     * @param createReqVO InletAnalysisMonthCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody InletAnalysisMonthCreateReqVO createReqVO) {
        return success(inletAnalysisMonthService.create(createReqVO));
    }

    /**
     * 批量创建月来水分析
     * @description 多个对象保存
     * @param lists  InletAnalysisMonthCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<InletAnalysisMonthCreateReqVO> lists) {
        return success(inletAnalysisMonthService.createBatch(lists));
    }

    /**
     * 更新月来水分析
     * @description 单个对象修改
     * @param updateReqVO InletAnalysisMonthUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody InletAnalysisMonthUpdateReqVO updateReqVO) {
        inletAnalysisMonthService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新月来水分析
     * @description 批量更新
     * @param lists 批量更新列表 InletAnalysisMonthUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<InletAnalysisMonthUpdateReqVO> lists) {
        return success(inletAnalysisMonthService.updateBatch(lists));
    }

    /**
     * 删除月来水分析
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        inletAnalysisMonthService.delete(id);
        return success(true);
    }

    /**
     * 批量删除月来水分析
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(inletAnalysisMonthService.deleteBatch(ids));
    }

    /**
     * 获得月来水分析详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<InletAnalysisMonthDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<InletAnalysisMonthDetailRespVO> get(@RequestParam("id") Long id) {
        InletAnalysisMonthDO inletAnalysisMonth = inletAnalysisMonthService.get(id);
        return success(InletAnalysisMonthConvert.INSTANCE.convertDetail(inletAnalysisMonth));
    }

    /**
     * 月来水分析列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 InletAnalysisMonthQueryReqVO
     * @return CommonResult<List<InletAnalysisMonthRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<InletAnalysisMonthRespVO>> getList(@RequestQueryParam InletAnalysisMonthQueryReqVO queryReqVO) {
        List<InletAnalysisMonthDO> list = inletAnalysisMonthService.getList(queryReqVO);
        return success(InletAnalysisMonthConvert.INSTANCE.convertList(list));
    }

    /**
     * 月来水分析分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 InletAnalysisMonthPageReqVO
     * @return CommonResult<PageResult<InletAnalysisMonthRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<InletAnalysisMonthRespVO>> page(@RequestQueryParam InletAnalysisMonthPageReqVO pageVO) {
        PageResult<InletAnalysisMonthDO> pageResult = inletAnalysisMonthService.page(pageVO);
        return success(InletAnalysisMonthConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出月来水分析Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 InletAnalysisMonthExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam InletAnalysisMonthExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<InletAnalysisMonthDO> list = inletAnalysisMonthService.getList(queryReqVO);
        // 导出 Excel
        List<InletAnalysisMonthExcelVO> datas = InletAnalysisMonthConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "月来水分析", "xlsx"), queryReqVO.getExportSheetName(),
                                                    InletAnalysisMonthExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入月来水分析模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "月来水分析-导入模版.xls", "sheet1", InletAnalysisMonthExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入月来水分析Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-month:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<InletAnalysisMonthExcelVO> list = ExcelUtils.read(file, InletAnalysisMonthExcelVO.class);
        return success(inletAnalysisMonthService.importExcel(list, isUpdate));
    }
}