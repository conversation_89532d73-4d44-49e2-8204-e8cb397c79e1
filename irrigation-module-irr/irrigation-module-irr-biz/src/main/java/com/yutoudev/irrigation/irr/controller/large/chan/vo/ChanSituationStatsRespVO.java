package com.yutoudev.irrigation.irr.controller.large.chan.vo;

import lombok.Data;

/**
 * 大屏 - 水渠评价情况统计 Response VO
 *
 * <AUTHOR>
 */
@Data
public class ChanSituationStatsRespVO {

    /**
     * 水渠总长度(km)
     */
    private Double chanLen;

    /**
     * 情况A类长度(km)
     */
    private Double situationLengthA;

    /**
     * 情况B类长度(km)
     */
    private Double situationLengthB;

    /**
     * 情况C类长度(km)
     */
    private Double situationLengthC;

    /**
     * 情况D类长度
     */
    private Double situationLengthD;

    /**
     * 现状建筑物完好率(%) （A）/总数
     */
    private Double intactRate;

}