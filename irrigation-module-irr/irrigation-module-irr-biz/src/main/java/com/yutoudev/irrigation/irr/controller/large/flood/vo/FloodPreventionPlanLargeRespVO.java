package com.yutoudev.irrigation.irr.controller.large.flood.vo;

import com.yutoudev.irrigation.irr.controller.admin.floodplan.vo.FloodPreventionPlanBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 大屏接口 - 防洪预案 Response VO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FloodPreventionPlanLargeRespVO extends FloodPreventionPlanBaseVO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 预案等级标签
     */
    private String planLevelLabel;

    /**
     * 预案状态标签
     */
    private String planStatusLabel;
} 