package com.yutoudev.irrigation.irr.controller.admin.rainfallprocess.vo.runoffitem;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 流域降雨过程净雨ExcelVO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class RainfallProcessRunoffItemExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 流域降雨过程ID
     */
    @ExcelProperty("流域降雨过程ID")
    private Long processId;

    /**
     * 时间
     */
    @ExcelProperty("时间")
    private LocalDateTime reportTime;

    /**
     * 降雨量
     */
    @ExcelProperty("P")
    private Double precipitation;

    /**
     * 蒸发量
     */
    @ExcelProperty("E")
    private Double evaporation;

    /**
     * 蒸散发量
     *
     * @mock 蒸发量*折算系数
     */
    @ExcelProperty("EP")
    private Double evapotranspiration;

    /**
     * 总土壤含水量
     */
    @ExcelProperty("W")
    private Double paramW;

    /**
     * 上层土壤含水量
     */
    @ExcelProperty("WU")
    private Double paramWu;

    /**
     * 下层土壤含水量
     */
    @ExcelProperty("WL")
    private Double paramWl;

    /**
     * 深层土壤含水量
     */
    @ExcelProperty("WD")
    private Double paramWd;

    /**
     * 自由水蓄量
     */
    @ExcelProperty("S")
    private Double paramS;

    /**
     * 上层土壤蒸发量
     */
    @ExcelProperty("EU")
    private Double paramEu;

    /**
     * 下层土壤蒸发量
     */
    @ExcelProperty("EL")
    private Double paramEl;

    /**
     * 深层土壤蒸发量
     */
    @ExcelProperty("ED")
    private Double paramEd;

    /**
     * 净雨量
     */
    @ExcelProperty("PE")
    private Double paramPe;

    /**
     * 总径流量
     */
    @ExcelProperty("R")
    private Double paramR;

    /**
     * 地表径流量
     */
    @ExcelProperty("RS")
    private Double paramRs;

    /**
     * 壤中流量
     */
    @ExcelProperty("RI")
    private Double paramRi;

    /**
     * 地下径流量
     */
    @ExcelProperty("RG")
    private Double paramRg;

    /**
     * 出口断面流量
     */
    @ExcelProperty("Q")
    private Double paramQ;

    /**
     * 单元流域在各子河段出口断面形成的出流
     */
    @ExcelProperty("O")
    private String paramOStr;

    private List<Double> paramO;

    /**
     * 单元流域在全流域出口断面形成的出流
     */
    @ExcelProperty("O2")
    private Double paramO2;
}
