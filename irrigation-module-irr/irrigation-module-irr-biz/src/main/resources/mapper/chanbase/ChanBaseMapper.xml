<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.chanbase.ChanBaseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectChanAndEquipListBySwhsId"
            resultType="com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanEquipVO">
        select cb.id,cb.chan_code,cb.chan_name,cb.chan_type,wge.equip_id from  irr_chan_base cb
            left join irr_chan_water_gate cwg on cb.id = cwg.chan_id
            left join irr_water_gate wg on wg.id = cwg.gate_id
            left join irr_water_gate_equip wge on wg.id = wge.gate_id

        where cb.swhs_id = #{swhsId} and cwg.gate_type = 1 and (wge.dev_type = 1201 or wge.dev_type = 1202 or wge.dev_type = 1203) and cb.deleted = 0  and wg.deleted = 0
        order by cb.chan_type
    </select>

    <select id="selectChanAndEquipListByChanIds"
            resultType="com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanEquipVO">
        select cb.id,cb.chan_code,cb.chan_name,cb.chan_type,wge.equip_id from  irr_chan_base cb
            left join irr_chan_water_gate cwg on cb.id = cwg.chan_id
            left join irr_water_gate wg on wg.id = cwg.gate_id
            left join irr_water_gate_equip wge on wg.id = wge.gate_id

        where cb.id IN (
        <foreach collection="chanIds" item="item" index="index" separator=",">
            #{item}
        </foreach>
        ) and cwg.gate_type = 1 and (wge.dev_type = 1201 or wge.dev_type = 1202 or wge.dev_type = 1203) and cb.deleted = 0  and wg.deleted = 0
        order by cb.chan_type

    </select>

    <select id="equipWaterYieldList"
            resultType="com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.EquipWaterYieldVO">
        SELECT
            equip_id,
            DATE_FORMAT(irr_meas_ind_day.day, '%Y-%m') as month,
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '01',water_supply/10000,null)) AS 'day_1',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '02',water_supply/10000,null)) AS 'day_2',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '03',water_supply/10000,null)) AS 'day_3',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '04',water_supply/10000,null)) AS 'day_4',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '05',water_supply/10000,null)) AS 'day_5',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '06',water_supply/10000,null)) AS 'day_6',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '07',water_supply/10000,null)) AS 'day_7',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '08',water_supply/10000,null)) AS 'day_8',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '09',water_supply/10000,null)) AS 'day_9',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '10',water_supply/10000,null)) AS 'day_10',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '11',water_supply/10000,null)) AS 'day_11',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '12',water_supply/10000,null)) AS 'day_12',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '13',water_supply/10000,null)) AS 'day_13',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '14',water_supply/10000,null)) AS 'day_14',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '15',water_supply/10000,null)) AS 'day_15',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '16',water_supply/10000,null)) AS 'day_16',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '17',water_supply/10000,null)) AS 'day_17',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '18',water_supply/10000,null)) AS 'day_18',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '19',water_supply/10000,null)) AS 'day_19',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '20',water_supply/10000,null)) AS 'day_20',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '21',water_supply/10000,null)) AS 'day_21',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '22',water_supply/10000,null)) AS 'day_22',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '23',water_supply/10000,null)) AS 'day_23',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '24',water_supply/10000,null)) AS 'day_24',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '25',water_supply/10000,null)) AS 'day_25',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '26',water_supply/10000,null)) AS 'day_26',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '27',water_supply/10000,null)) AS 'day_27',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '28',water_supply/10000,null)) AS 'day_28',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '29',water_supply/10000,null)) AS 'day_29',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '30',water_supply/10000,null)) AS 'day_30',
            SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = '31',water_supply/10000,null)) AS 'day_31',
            avg(avg_water_flow) as avg_water_flow
        FROM
            irr_meas_ind_day where 1=1 and equip_id in (
            <foreach collection="equipIds" item="item" index="index" separator=",">
                #{item}
            </foreach>
            ) and day BETWEEN #{startDate} and #{endDate}
        GROUP BY
            equip_id,month
        ORDER BY
            equip_id,month
    </select>
    <select id="selectFlowGateEquipListBySwhsId"
            resultType="com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanEquipVO">
        select 0 as id,0 as chan_code,wg.wain_name as chan_name, 100 as chan_type,wge.equip_id from irr_swhs_base sb
            left join irr_water_gate wg on sb.base_flow_gate_id = wg.id
            left join irr_water_gate_equip wge on wg.id = wge.gate_id
        where sb.id = #{swhsId} and (wge.dev_type = 1201 or wge.dev_type = 1202 or wge.dev_type = 1203) and wg.deleted = 0 and sb.deleted = 0
    </select>

    <select id="statisticsLining" resultType="com.yutoudev.irrigation.irr.controller.large.chan.vo.ChanLiningStatsRespVO">
        SELECT
            SUM(chan_len) as chan_len,
            SUM(lining_len) as lining_len
        FROM irr_chan_base
        WHERE deleted = 0
    </select>

    <select id="pageLining" resultType="com.yutoudev.irrigation.irr.controller.large.chan.vo.ChanLiningRespVO">
        SELECT
            id,
            chan_name,
            chan_len,
            lining_len,
            CASE
                WHEN chan_len > 0 THEN ROUND(IFNULL(lining_len, 0) / chan_len * 100, 2)
                ELSE 0
                END as completion_rate
        FROM irr_chan_base
        ${ew.customSqlSegment}
        ORDER BY completion_rate DESC
    </select>

    <select id="pageRegimen" resultType="com.yutoudev.irrigation.irr.controller.large.regimen.vo.ChanRegimenRespVO">
        SELECT
            *
        FROM
            (SELECT
                 b.id,
                 b.chan_name,
                 wge.equip_id as equipId,
                 t.calc_water_flow as flow,
                 b.max_warning_value as alarmWaterLevel,
                 t.water_level as realTimeWaterLevel,
                 t.report_time as reportTime,
                 case
                     when f.create_time is null then
                         (select count(1) from ew_warn_info where warn_type = 7 and equip_id = wge.equip_id )
                     when f.create_time is not null and f.deleted = 1 then
                         (select count(1) from ew_warn_info where warn_type = 7 and equip_id = wge.equip_id  and create_time >= now())
                     when f.create_time is not null and f.deleted = 0 then
                         (select count(1) from ew_warn_info where warn_type = 7 and equip_id = wge.equip_id and create_time >= f.create_time )
                     end as alarmCount
             FROM irr_chan_base b
                      LEFT JOIN (
                 SELECT
                     chan_id,
                     min(gate_id) as gate_id
                 FROM
                     irr_chan_water_gate
                 WHERE
                     gate_type = 1
                 GROUP BY
                     chan_id
                 ORDER BY chan_id
             ) cwg on cwg.chan_id = b.id
                      LEFT JOIN (
                 SELECT
                     gate_id,
                     min(equip_id) as equip_id
                 FROM
                     irr_water_gate_equip
                 WHERE
                     st_type = 1
                   AND dev_type IN (1201, 1202, 1203)
                 GROUP BY
                     gate_id
                 ORDER BY gate_id
             ) wge ON wge.gate_id = cwg.gate_id
                      LEFT JOIN (
                 SELECT
                     equip_id,
                     water_level,
                     calc_water_flow,
                     report_time
                 FROM
                     irr_meas_ind_time
                 WHERE
                    report_time >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                 GROUP BY
                    equip_id
                 ORDER BY
                     report_time DESC
             ) as t ON wge.equip_id = t.equip_id
                      LEFT JOIN (
                 SELECT t.create_time,t.equip_id,t.deleted
                 FROM ew_warn_info_flicker t
                          INNER JOIN (
                     SELECT equip_id, MAX(create_time) as max_time
                     FROM ew_warn_info_flicker
                     GROUP BY equip_id
                 ) m ON t.equip_id = m.equip_id AND t.create_time = m.max_time
             ) as f on f.equip_id = wge.equip_id
             WHERE b.deleted = 0) as data_result
        ORDER BY id
    </select>
    <select id="listEquipByEquipTypeAndChanTypes"
            resultType="com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO">
        SELECT * FROM irr_equip_base
            WHERE id IN (
                -- 从渠道直接关联的设备ID
                SELECT equip.id
                FROM irr_chan_base AS chan
                JOIN irr_chan_equip AS ce ON chan.id = ce.chan_id
                JOIN irr_equip_base AS equip ON ce.equip_id = equip.id
                WHERE chan.deleted = 0 AND equip.deleted = 0 AND equip.st_type = #{stType} AND chan.chan_type IN (
                <foreach collection="chanTypes" item="item" index="index" separator=",">
                    #{item}
                </foreach>
                )

                UNION

                -- 从渠道关联的水闸间接关联的设备ID
                SELECT equip.id
                FROM irr_chan_base AS chan
                         JOIN irr_chan_water_gate cwg ON chan.id = cwg.chan_id
                         JOIN irr_water_gate wg ON cwg.gate_id = wg.id
                         JOIN irr_water_gate_equip wge ON wg.id = wge.gate_id
                         JOIN irr_equip_base equip ON wge.equip_id = equip.id
                WHERE chan.deleted = 0 AND equip.deleted = 0 AND wg.deleted = 0 AND equip.st_type = #{stType} AND chan.chan_type IN (
                <foreach collection="chanTypes" item="item" index="index" separator=",">
                    #{item}
                </foreach>
                )
            ) ORDER BY id;
    </select>

    <select id="pageSituation" resultType="com.yutoudev.irrigation.irr.controller.large.chan.vo.ChanSituationRespVO">
        SELECT
            t.*,
            COALESCE(ROUND( t.situationLengthA * 100.0 / NULLIF( t.chanLen, 0 ), 2 ),0) AS intactRate
        FROM
            (
                SELECT
                    id id,
                    chan_name chanName,
                    COALESCE(chan_len, 0) chanLen,
                    COALESCE(situation_length_a, 0) situationLengthA,
                    COALESCE(situation_length_b, 0) situationLengthB,
                    COALESCE(situation_length_c, 0) situationLengthC,
                    COALESCE(situation_length_d, 0) situationLengthD
                FROM
                    irr_chan_base
                        ${ew.customSqlSegment}
            ) t order by intactRate desc
    </select>

    <select id="statisticsSituation" resultType="com.yutoudev.irrigation.irr.controller.large.chan.vo.ChanSituationStatsRespVO">
        SELECT
            t.*,
            ROUND( t.situationLengthA * 100.0 / NULLIF( t.chanLen, 0 ), 2 )  AS intactRate
        FROM
            (
                SELECT
                    COALESCE(SUM(chan_len), 0) AS chanLen,
                    COALESCE(SUM(situation_length_a), 0) AS situationLengthA,
                    COALESCE(SUM(situation_length_b), 0) AS situationLengthB,
                    COALESCE(SUM(situation_length_c), 0) AS situationLengthC,
                    COALESCE(SUM(situation_length_d), 0) AS situationLengthD
                FROM
                    irr_chan_base
                WHERE
                    deleted = 0
            ) t
    </select>


    <select id="selectWaterSupply" resultType="com.yutoudev.irrigation.irr.controller.large.chan.vo.WaterSupplyVO">
        SELECT
            t1.equip_id,
            ROUND(sum(t1.water_supply) / 10000,2) AS water_supply,
            t2.chan_id
        FROM
            irr_meas_ind_day t1
                LEFT JOIN irr_chan_equip_query t2 ON t1.equip_id = t2.equip_id
        WHERE
            1 = 1
          AND EXISTS (
            SELECT
                1
            FROM
                irr_equip_base
            WHERE
                st_type = 1
              AND (dev_type = 1201 OR dev_type = 1202 OR dev_type = 1203)
              AND t1.equip_id = irr_equip_base.id
        )
          AND DAY BETWEEN #{startDate} AND #{endDate}  and t2.chan_id is not null
        GROUP BY
            t1.equip_id
    </select>

    <select id="refreshIrrChanEquip" statementType="CALLABLE">
        call RefreshIrrChanEquip()
    </select>
</mapper>
