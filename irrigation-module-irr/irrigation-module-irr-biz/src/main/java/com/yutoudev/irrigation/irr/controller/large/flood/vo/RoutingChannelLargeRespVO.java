package com.yutoudev.irrigation.irr.controller.large.flood.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 渠道调洪演算ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RoutingChannelLargeRespVO {

    /**
     * 渠道ID
     */
    private Long chanId;

    /**
     * 渠道名称
     */
    private String chanName;

    /**
     * 调整后设计流量
     */
    private Double calcDesignFlow;

    /**
     * 计算截止桩号
     */
    private String calcStakeNumber;

    /**
     * 溢流标志
     *
     * @mock 0正常 1溢流
     */
    private Integer overflow;

    /**
     * 区间流量
     */
    private Double intervalFlow;

    /**
     * 最大来水量
     */
    private Double maxInflow;

    /**
     * 最大下泄流量
     */
    private Double maxOutFlow;

    /**
     * 最大下泄时间
     */
    private LocalDateTime maxOutFlowTime;
}