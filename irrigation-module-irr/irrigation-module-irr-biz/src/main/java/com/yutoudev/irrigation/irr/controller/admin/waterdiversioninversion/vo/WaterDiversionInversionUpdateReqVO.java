package com.yutoudev.irrigation.irr.controller.admin.waterdiversioninversion.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 引水量反演更新RequestVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WaterDiversionInversionUpdateReqVO extends WaterDiversionInversionBaseVO {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}