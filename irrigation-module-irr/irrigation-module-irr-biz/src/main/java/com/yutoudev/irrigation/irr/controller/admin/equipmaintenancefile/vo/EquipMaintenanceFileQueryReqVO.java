package com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.equipmaintenance.EquipMaintenanceFileDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 设备维护附件list查询RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护附件list查询RequestVO，参数和 EquipMaintenanceFilePageReqVO 是一致的
 * @time 2024-10-27 10:33:14
 */
@Data
public class EquipMaintenanceFileQueryReqVO extends QueryCriteria<EquipMaintenanceFileDO> {

    /**
     * ID
     */
    private Long id;

    /**
     * 设备维护ID
     */
    private Long maintenanceId;

    /**
     * 附件id
     */
    private Long fileId;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 附件url
     */
    private String fileUrl;

    /**
     * 附件媒体类型
     */
    private String fileType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
