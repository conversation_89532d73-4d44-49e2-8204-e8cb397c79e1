package com.yutoudev.irrigation.irr.controller.admin.allocationplanestimate.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationplanestimate.AllocationPlanEstimateDO;

/**
 *
 * 配水调度计划估算分页RequestVO
 * @description 管理后台-配水调度计划估算分页RequestVO
 * <AUTHOR>
 * @time 2024-09-16 11:16:31
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AllocationPlanEstimatePageReqVO extends PageCriteria<AllocationPlanEstimateDO> {

    /**
     * 配水计划ID
     * 
     */
    private Long planId;

    /**
     * 类型
     * @mock ：0，年；1，月；2，日
     */
    private Integer type;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水源名称
     * 
     */
    private String swhsName;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水渠名称
     * 
     */
    private String chanName;

    /**
     * 子级水渠ID
     * 
     */
    private Long childChanId;

    /**
     * 对应当前水渠或水源出水口ID
     * 
     */
    private Long gateId;

    /**
     * 出水口名称
     * 
     */
    private String gateName;

    /**
     * 计划供水
     * @mock （m³）
     */
    private Double supplyWater;

    /**
     * 计划供水开始时间
     * 
     */
    private LocalDateTime startTime;

    /**
     * 计划供水结束时间
     * 
     */
    private LocalDateTime endTime;

    /**
     * 损耗值
     * 
     */
    private Double loss;

    /**
     * 径流时间
     * @mock （分钟）
     */
    private Long runoffTime;

}
