package com.yutoudev.irrigation.irr.controller.large.statistics.vo;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 供用水分析 ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-供用水分析ResponseVO
 * @time 2025-03-21
 */
@Data
@ToString(callSuper = true)
public class WaterProvideAndUseRespVO {

    /**
     * 可供水量 万m³
     */
    private Double provide;

    /**
     * 保灌天数
     */
    private Integer irrigationDays;

    /**
     * 需水量 万m³
     */
    private Double water;

    /**
     * 月份供用水明细
     */
    private List<WaterDemandMonthRespVO> months;


}
