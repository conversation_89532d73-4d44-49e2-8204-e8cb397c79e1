package com.yutoudev.irrigation.irr.controller.large.plan;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.plan.vo.droughtplan.DroughtPlanPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.plan.vo.droughtplan.DroughtPlanQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.plan.vo.droughtplan.DroughtPlanRespVO;
import com.yutoudev.irrigation.irr.controller.large.plan.vo.DroughtPlanLargeRespVO;
import com.yutoudev.irrigation.irr.convert.plan.DroughtPlanConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.plan.DroughtPlanDO;
import com.yutoudev.irrigation.irr.enums.DictTypeConstants;
import com.yutoudev.irrigation.irr.service.plan.DroughtPlanService;
import com.yutoudev.irrigation.module.system.api.dict.DictDataApi;
import com.yutoudev.irrigation.module.system.api.dict.dto.DictDataRespDTO;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 抗旱预案 大屏接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/drought-plan")
@Validated
public class LargeDroughtPlanController {

    private static final String MODULE_NAME = "抗旱预案";

    @Resource
    private DroughtPlanService<DroughtPlanDO> droughtPlanService;

    @Resource
    private DictDataApi dictDataApi;

    /**
     * 抗旱预案列表
     *
     * @param queryReqVO 查询条件 DroughtPlanQueryReqVO
     * @return CommonResult<List < DroughtPlanRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<DroughtPlanRespVO>> getList(@RequestQueryParam DroughtPlanQueryReqVO queryReqVO) {
        List<DroughtPlanDO> list = droughtPlanService.getList(queryReqVO);
        return success(DroughtPlanConvert.INSTANCE.convertList(list));
    }

    /**
     * 抗旱预案分页
     *
     * @param pageVO 查询条件 DroughtPlanPageReqVO
     * @return CommonResult<PageResult < DroughtPlanRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<DroughtPlanLargeRespVO>> page(@RequestQueryParam DroughtPlanPageReqVO pageVO) {
        PageResult<DroughtPlanDO> pageResult = droughtPlanService.page(pageVO);
        PageResult<DroughtPlanLargeRespVO> result = DroughtPlanConvert.INSTANCE.convertLargePage(pageResult);

        for (DroughtPlanLargeRespVO resp : result.getList()) {
            if (Objects.nonNull(resp.getPlanLevel())) {
                DictDataRespDTO dict = dictDataApi.getDictData(DictTypeConstants.IRR_DROUGHT_LEVEL, resp.getPlanLevel().toString());
                if (Objects.nonNull(dict)) {
                    resp.setPlanLevelLabel(dict.getLabel());
                }
            }
            if (Objects.nonNull(resp.getPlanStatus())) {
                DictDataRespDTO dict = dictDataApi.getDictData(DictTypeConstants.FLOOD_PLAN_STATUS, resp.getPlanStatus().toString());
                resp.setPlanStatusLabel(dict.getLabel());
            }
        }
        return success(result);
    }
}