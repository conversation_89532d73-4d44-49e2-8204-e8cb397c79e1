<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndHourMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="sumRainfallVolume" resultType="java.lang.Double">
        SELECT SUM(rainfall_volume) rainfall_volume FROM irr_meas_ind_hour WHERE equip_id=#{equipId} AND `hour_time` BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="getMeasWaterVolumeSum" resultType="java.lang.Double">
        SELECT SUM(water_volume) water_volume FROM irr_meas_ind_hour WHERE equip_id=#{equipId} AND `hour_time` > #{startTime} AND `hour_time` &lt;= #{endTime}
    </select>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO irr_meas_ind_hour(
            id,
            equip_id,
            central_id,
            dev_id,
            hour_time,
            rainfall_volume,
            water_level,
            water_flow,
            water_volume,
            reservoir_capacity,
            reservoir_inflow,
            reservoir_outflow,
            reservoir_capacity_diff,
            reservoir_inflow_volume,
            reservoir_outflow_volume,
            voltage,
            update_time
        )
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.equipId},
                #{entity.centralId},
                #{entity.devId},
                #{entity.hourTime},
                #{entity.rainfallVolume},
                #{entity.waterLevel},
                #{entity.waterFlow},
                #{entity.waterVolume},
                #{entity.reservoirCapacity},
                #{entity.reservoirInflow},
                #{entity.reservoirOutflow},
                #{entity.reservoirCapacityDiff},
                #{entity.reservoirInflowVolume},
                #{entity.reservoirOutflowVolume},
                #{entity.voltage},
                #{entity.updateTime}
            )
        </foreach>
        ON duplicate KEY UPDATE
        equip_id = VALUES(equip_id),
        central_id = VALUES(central_id),
        dev_id = VALUES(dev_id),
        hour_time = VALUES(hour_time),
        rainfall_volume = VALUES(rainfall_volume),
        water_level = VALUES(water_level),
        water_flow = VALUES(water_flow),
        water_volume = VALUES(water_volume),
        reservoir_capacity = VALUES(reservoir_capacity),
        reservoir_inflow = VALUES(reservoir_inflow),
        reservoir_outflow = VALUES(reservoir_outflow),
        reservoir_capacity_diff = VALUES(reservoir_capacity_diff),
        reservoir_inflow_volume = VALUES(reservoir_inflow_volume),
        reservoir_outflow_volume = VALUES(reservoir_outflow_volume),
        voltage = VALUES(voltage),
        update_time = VALUES(update_time)
    </insert>

    <select id="getWaterLevelByStatHour" resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourStatHourVO">
        SELECT
            equip_id,
            central_id,
            dev_id,
            MAX(CASE WHEN hour_time = #{morning} THEN water_level END) as water_level_morning,
            MAX(CASE WHEN hour_time = #{night} THEN water_level END) as water_level_night
        FROM
            irr_meas_ind_hour
        WHERE
            central_id = #{centralId}
            and dev_id = #{devId}
            and
            hour_time IN (#{morning}, #{night})
        GROUP BY
            equip_id
        ORDER BY
            equip_id
    </select>

    <select id="getShortRainfall"
            resultType="com.yutoudev.irrigation.irr.controller.app.measind.vo.ShortRainfallRespVO">
        SELECT
        hours AS `hour`,
        ROUND(COALESCE(SUM( CASE WHEN hour_time >= NOW() - INTERVAL hours HOUR THEN rainfall_volume ELSE 0 END ), 0), 1)
        AS rainfall
        FROM
        irr_meas_ind_hour,
        (
        SELECT
        1 AS hours UNION ALL
        SELECT
        3 UNION ALL
        SELECT
        6 UNION ALL
        SELECT
        12 UNION ALL
        SELECT
        24 UNION ALL
        SELECT
        48 UNION ALL
        SELECT
        72
        ) AS time_ranges
        WHERE
        hour_time >= NOW() - INTERVAL 72 HOUR
        AND central_id = #{centralId} and dev_id = #{devId}
        GROUP BY
        hours
        ORDER BY
        hours
    </select>
    <select id="listInflowByPeriod"
            resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourRespVO">
        SELECT
        id, equip_id,
        DATE_FORMAT(hour_time, '%Y-%m-%d %H:00:00') AS hour_time,
        AVG(reservoir_inflow) AS reservoir_inflow,
        MAX(water_level) AS water_level
        FROM irr_meas_ind_hour
        WHERE equip_id = #{equipId}
        AND hour_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY
        FLOOR((UNIX_TIMESTAMP(hour_time) - UNIX_TIMESTAMP(#{startTime})) / (3600*#{intervalTime}))
        ORDER BY
        hour_time;
    </select>

    <select id="staticsMonthInflow"
            resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourStaticsMonthInflowRespVO">
        SELECT
        EXTRACT(MONTH FROM hour_time) AS month,
        COALESCE(SUM(reservoir_inflow_volume), 0) AS total_inflow
        FROM
        irr_meas_ind_hour
        WHERE equip_id = #{equipId}
        AND hour_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY
        EXTRACT(MONTH FROM hour_time)
        ORDER BY
        month;
    </select>

    <select id="staticsMonthReservoirCapacity"
            resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourStaticsMonthReservoirCapacityRespVO">
        SELECT
        equip_id,
        EXTRACT(YEAR FROM hour_time) AS year,
        EXTRACT(MONTH FROM hour_time) AS month,
        reservoir_capacity
        FROM
        irr_meas_ind_hour
        WHERE equip_id = #{equipId}
        AND EXTRACT(DAY FROM hour_time) = 1
        AND EXTRACT(HOUR FROM hour_time) = 8
        AND EXTRACT(MINUTE FROM hour_time) = 0
        AND EXTRACT(SECOND FROM hour_time) = 0
        AND hour_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY
        equip_id, year, month;
    </select>
    <select id="staticsRainFallListByCatchmentAreaId"
            resultType="com.yutoudev.irrigation.irr.controller.app.equip.vo.RainfallSiteRespVO">
        SELECT
        equip.id AS id,
        equip.st_name AS st_name,
        equip.central_id AS central_id,
        equip.dev_id AS dev_id,
        equip.pic_url AS pic_url,
        equip.`online` AS `online`,
        equip.take_photo AS take_photo,
        equip.protocol AS protocol,
        realTime.rainfall AS rainfall,
        realTime.day_rainfall AS day_rainfall,
        realTime.month_rainfall AS month_rainfall,
        realTime.v14 AS voltage,
        indHour.h3Value AS h3value,
        indHour.h6Value AS h6Value,
        indHour.h12Value AS h12Value,
        indHour.h24Value AS h24Value,
        warnInfo.id AS warn_id,
        warnInfo.warn_content AS warn_content,
        warnLevel.warn_level AS warn_level,
        warnLevel.warn_level_name AS warn_level_name,
        warnInfo.create_time AS warn_time
        FROM
        irr_catchment_area_equip AS cae
        LEFT JOIN irr_equip_base AS equip ON cae.equip_id = equip_id AND cae.central_id = equip.central_id AND
        cae.dev_id = equip.dev_id
        INNER JOIN (
        SELECT
        equip_id,
        central_id,
        dev_id,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 3 HOUR THEN rainfall_volume ELSE 0 END ) AS h3Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 6 HOUR THEN rainfall_volume ELSE 0 END ) AS h6Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 12 HOUR THEN rainfall_volume ELSE 0 END ) AS h12Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 24 HOUR THEN rainfall_volume ELSE 0 END ) AS h24Value
        FROM
        irr_meas_ind_hour
        WHERE
        equip_id IN ( SELECT equip_id FROM irr_catchment_area_equip WHERE catchment_id = #{areaId} )
        AND hour_time >= NOW() - INTERVAL 24 HOUR
        GROUP BY
        equip_id,
        central_id,
        dev_id
        ) AS indHour ON indHour.equip_id = equip.id AND indHour.central_id = equip.central_id AND indHour.dev_id =
        equip.dev_id
        INNER JOIN irr_meas_realtime AS realTime ON realTime.equip_id = equip.id AND realTime.central_id =
        equip.central_id AND realTime.dev_id = equip.dev_id
        LEFT JOIN (
        SELECT
        id,
        create_time,
        equip_id,
        warn_level_id,
        warn_content
        FROM
        ( SELECT
        t.*,
        ROW_NUMBER() OVER ( PARTITION BY t.equip_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
        FROM
        ew_warn_info t
        WHERE
        t.deleted = 0
        AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
        ) AS sub
        WHERE
        rn = 1
        ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
        WHERE cae.catchment_id = #{areaId}

    </select>
    <select id="staticsRainFallListByCatchmentArea"
            resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.RainfallCatchmentSiteRespVO">
        SELECT
        equip.id AS id,
        equip.st_name AS st_name,
        equip.central_id AS central_id,
        equip.dev_id AS dev_id,
        equip.`online` AS `online`,
        ca.id AS catchment_id,
        ca.`name` AS catchment_name,
        cae.area AS catchment_area,
        ca.swhs_id AS swhs_id,
        ca.chan_id AS chan_id,
        realTime.rainfall AS rainfall,
        realTime.day_rainfall AS day_rainfall,
        realTime.month_rainfall AS month_rainfall,
        realTime.v14 AS voltage,
        realTime.update_time AS measure_time,
        indHour.h1Value AS h1value,
        indHour.h3Value AS h3value,
        indHour.h6Value AS h6Value,
        indHour.h12Value AS h12Value,
        indHour.h24Value AS h24Value,
        indHour.h48Value AS h48Value,
        warnInfo.id AS warn_id,
        warnInfo.warn_content AS warn_content,
        warnInfo.warn_color AS warn_color,
        warnLevel.warn_level AS warn_level,
        warnLevel.warn_level_name AS warn_level_name,
        warnInfo.create_time AS warn_time
        FROM
        irr_catchment_area AS ca
        LEFT JOIN irr_catchment_area_equip AS cae ON ca.id = cae.catchment_id
        LEFT JOIN irr_equip_base AS equip ON cae.equip_id = equip_id AND cae.central_id = equip.central_id AND
        cae.dev_id = equip.dev_id
        LEFT JOIN (
        SELECT
        equip_id,
        central_id,
        dev_id,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 1 HOUR THEN rainfall_volume ELSE 0 END ) AS h1Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 3 HOUR THEN rainfall_volume ELSE 0 END ) AS h3Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 6 HOUR THEN rainfall_volume ELSE 0 END ) AS h6Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 12 HOUR THEN rainfall_volume ELSE 0 END ) AS h12Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 24 HOUR THEN rainfall_volume ELSE 0 END ) AS h24Value,
        SUM( CASE WHEN hour_time >= NOW() - INTERVAL 48 HOUR THEN rainfall_volume ELSE 0 END ) AS h48Value
        FROM
        irr_meas_ind_hour
        WHERE
        equip_id IN ( SELECT equip_id FROM irr_catchment_area_equip )
        AND hour_time >= NOW() - INTERVAL 48 HOUR
        GROUP BY
        equip_id,
        central_id,
        dev_id
        ) AS indHour ON indHour.equip_id = equip.id AND indHour.central_id = equip.central_id AND indHour.dev_id =
        equip.dev_id
        LEFT JOIN irr_meas_realtime AS realTime ON realTime.equip_id = equip.id AND realTime.central_id =
        equip.central_id AND realTime.dev_id = equip.dev_id
        LEFT JOIN (
        SELECT
        id,
        create_time,
        equip_id,
        warn_level_id,
        warn_color,
        warn_content
        FROM
        ( SELECT
        t.*,
        ROW_NUMBER() OVER ( PARTITION BY t.equip_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
        FROM
        ew_warn_info t
        WHERE
        t.deleted = 0
        AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
        ) AS sub
        WHERE
        rn = 1
        ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id

    </select>

</mapper>
