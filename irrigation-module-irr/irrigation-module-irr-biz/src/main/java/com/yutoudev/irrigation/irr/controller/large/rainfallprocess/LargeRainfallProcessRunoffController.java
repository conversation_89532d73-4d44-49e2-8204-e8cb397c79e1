package com.yutoudev.irrigation.irr.controller.large.rainfallprocess;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.large.rainfallprocess.vo.RainfallProcessRunoffLargeRespVO;
import com.yutoudev.irrigation.irr.controller.large.rainfallprocess.vo.RainfallProcessRunoffUhLargeRespVO;
import com.yutoudev.irrigation.irr.convert.rainfallprocess.RainfallProcessRunoffConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.RainfallProcessRunoffDO;
import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.RainfallProcessRunoffItemDO;
import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.RainfallProcessRunoffUhDO;
import com.yutoudev.irrigation.irr.service.rainfallprocess.RainfallProcessRunoffItemService;
import com.yutoudev.irrigation.irr.service.rainfallprocess.RainfallProcessRunoffService;
import com.yutoudev.irrigation.irr.service.rainfallprocess.RainfallProcessRunoffUhService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 流域降雨产流过程
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/rainfall-process-runoff")
@Validated
public class LargeRainfallProcessRunoffController {

    private static final String MODULE_NAME = "流域降雨产流过程-大屏";

    @Resource
    private RainfallProcessRunoffService<RainfallProcessRunoffDO> rainfallProcessRunoffService;

    @Resource
    private RainfallProcessRunoffItemService<RainfallProcessRunoffItemDO> rainfallProcessRunoffItemService;

    @Resource
    private RainfallProcessRunoffUhService<RainfallProcessRunoffUhDO> rainfallProcessRunoffUhService;

    /**
     * 获得最后流域降雨产流过程详情
     */
    @GetMapping("/last")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<RainfallProcessRunoffLargeRespVO> getLast() {
        RainfallProcessRunoffDO rainfallProcessRunoff = rainfallProcessRunoffService.getLast();
        RainfallProcessRunoffLargeRespVO respVO = RainfallProcessRunoffConvert.INSTANCE.convertLargeDetail(rainfallProcessRunoff);
        if (Objects.nonNull(rainfallProcessRunoff)) {
            List<RainfallProcessRunoffUhDO> unitHydrographFlowList = rainfallProcessRunoffUhService.getListByProcessId(rainfallProcessRunoff.getId());
            List<RainfallProcessRunoffItemDO> runoffItemList = rainfallProcessRunoffItemService.getListByProcessId(rainfallProcessRunoff.getId());
            Map<Long, RainfallProcessRunoffUhLargeRespVO> itemRespMap = new HashMap<>();
            double maxQ = 0;
            double yield = 0;
            for (RainfallProcessRunoffUhDO unitHydrographFlow : unitHydrographFlowList) {
                LocalDateTime reportTime = unitHydrographFlow.getReportTime();
                long key = reportTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                RainfallProcessRunoffUhLargeRespVO itemResp = new RainfallProcessRunoffUhLargeRespVO();
                itemResp.setId(unitHydrographFlow.getId());
                itemResp.setReportTime(reportTime);
                itemResp.setParamQ(unitHydrographFlow.getParamQ());
                itemResp.setMeasureInflow(unitHydrographFlow.getMeasureInflow());
                itemResp.setPrecipitation(0d);
                itemRespMap.put(key, itemResp);
                maxQ = Math.max(maxQ, unitHydrographFlow.getParamQ());
                yield += (unitHydrographFlow.getParamQ() * rainfallProcessRunoff.getDeltaTime() * 3600 / 10000);
            }


            double maxRainfall = 0;
            for (RainfallProcessRunoffItemDO runoffItem : runoffItemList) {
                LocalDateTime reportTime = runoffItem.getReportTime();
                long key = reportTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                if (itemRespMap.containsKey(key)) {
                    RainfallProcessRunoffUhLargeRespVO itemResp = itemRespMap.get(key);
                    itemResp.setPrecipitation(runoffItem.getPrecipitation());
                    itemRespMap.put(key, itemResp);
                    maxRainfall = Math.max(maxRainfall, runoffItem.getPrecipitation());
                }
            }
            respVO.setMaxOutflow(maxQ);
            respVO.setYield(PrecisionUtils.round(yield, 2));
            respVO.setMaxRainfall(maxRainfall);
            List<RainfallProcessRunoffUhLargeRespVO> items = itemRespMap.values().stream().sorted(Comparator.comparing(RainfallProcessRunoffUhLargeRespVO::getReportTime)).toList();
            respVO.setItems(items);
        }

        return success(respVO);
    }

}