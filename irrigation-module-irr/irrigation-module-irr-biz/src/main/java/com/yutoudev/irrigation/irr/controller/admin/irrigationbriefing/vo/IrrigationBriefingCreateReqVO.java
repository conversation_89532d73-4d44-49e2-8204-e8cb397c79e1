package com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 灌区简报创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IrrigationBriefingCreateReqVO extends IrrigationBriefingBaseVO {


    @Schema(description = "编辑单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "编辑单位不能为空")
    private String editorUnit;

    @Schema(description = "报告数据范围开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告数据范围开始时间不能为空")
    private LocalDateTime reportStartTime;

    @Schema(description = "报告数据范围结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告数据范围结束时间不能为空")
    private LocalDateTime reportEndTime;

}
