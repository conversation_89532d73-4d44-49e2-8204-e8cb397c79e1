package com.yutoudev.irrigation.irr.controller.admin.inletanalysismonth.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 月来水分析创建RequestVO
 * @description 管理后台-月来水分析创建RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:38
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisMonthCreateReqVO extends InletAnalysisMonthBaseVO {

}
