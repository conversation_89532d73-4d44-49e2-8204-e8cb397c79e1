package com.yutoudev.irrigation.irr.controller.admin.waterdiversioninversion.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 引水量反演ExcelVO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class WaterDiversionInversionExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 配水计划ID，多个以逗号隔开
     */
    @ExcelProperty("配水计划ID，多个以逗号隔开")
    private String planId;

    /**
     * 水源ID
     */
    @ExcelProperty("水源ID")
    private Long swhsId;

    /**
     * 水源名称
     */
    @ExcelProperty("水源名称")
    private String swhsName;

    /**
     * 水渠ID
     */
    @ExcelProperty("水渠ID")
    private Long chanId;

    /**
     * 水渠名称
     */
    @ExcelProperty("水渠名称")
    private String chanName;

    /**
     * 开始时间
     */
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 配水量
     */
    @ExcelProperty("配水量")
    private Double water;

    /**
     * 配水流量
     */
    @ExcelProperty("配水流量")
    private Double flowRate;

    /**
     * 渠道流速
     */
    @ExcelProperty("渠道流速")
    private Double chanFlow;

    /**
     * 子级水渠ID
     */
    @ExcelProperty("子级水渠ID")
    private Long childChanId;

    /**
     * 上级水渠ID
     */
    @ExcelProperty("上级水渠ID")
    private Long parentChanId;

    /**
     * 取水水口ID
     */
    @ExcelProperty("取水水口ID")
    private Long quGateId;

    /**
     * 取水站点ID
     */
    @ExcelProperty("取水站点ID")
    private Long quEquipId;

    /**
     * 出水水口ID
     */
    @ExcelProperty("出水水口ID")
    private Long chuGateId;

    /**
     * 出水站点ID
     */
    @ExcelProperty("出水站点ID")
    private Long chuEquipId;

    /**
     * 出水设备到进水设备水渠长度
     */
    @ExcelProperty("出水设备到进水设备水渠长度")
    private Long chuQuChanLength;

    /**
     * 渠道水深
     */
    @ExcelProperty("渠道水深")
    private Double waterLevel;

    /**
     * 反演基流
     */
    @ExcelProperty("反演基流")
    private Double baseFlow;

    /**
     * 闸前水深
     */
    @ExcelProperty("闸前水深")
    private Double beforeWaterLevel;

    /**
     * 损耗值
     */
    @ExcelProperty("损耗值")
    private Double loss;

    /**
     * 毛水量
     */
    @ExcelProperty("毛水量")
    private Double gross;

    /**
     * 径流时间
     */
    @ExcelProperty("径流时间")
    private Long runoffTime;

    /**
     * 设备开闸高度
     */
    @ExcelProperty("设备开闸高度")
    private Double gateHeight;

    /**
     * 设备默认高度
     */
    @ExcelProperty("设备默认高度")
    private Double gateDefaultHeight;

    /**
     * 反演情况
     *
     * @mock 0异常，1通过
     */
    @ExcelProperty("反演情况")
    private Integer status;

    /**
     * 情况说明
     */
    @ExcelProperty("情况说明")
    private String reason;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
