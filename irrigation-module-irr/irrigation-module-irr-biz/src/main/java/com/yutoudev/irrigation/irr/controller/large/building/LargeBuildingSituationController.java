package com.yutoudev.irrigation.irr.controller.large.building;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.building.vo.situation.BuildingSituationPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.building.vo.situation.BuildingSituationQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.building.vo.situation.BuildingSituationRespVO;
import com.yutoudev.irrigation.irr.controller.large.building.vo.BuildingSituationDangerStatsRespVO;
import com.yutoudev.irrigation.irr.controller.large.building.vo.BuildingSituationTypeCountRespVO;
import com.yutoudev.irrigation.irr.convert.building.BuildingSituationConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.building.BuildingSituationDO;
import com.yutoudev.irrigation.irr.service.building.BuildingSituationService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 工情 大屏接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/building-situation")
@Validated
public class LargeBuildingSituationController {

    private static final String MODULE_NAME = "工情";

    @Resource
    private BuildingSituationService<BuildingSituationDO> buildingSituationService;

    /**
     * 工情列表
     *
     * @param queryReqVO 查询条件 BuildingSituationQueryReqVO
     * @return CommonResult<List < BuildingSituationRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<BuildingSituationRespVO>> getList(@RequestQueryParam BuildingSituationQueryReqVO queryReqVO) {
        List<BuildingSituationDO> list = buildingSituationService.getList(queryReqVO);
        return success(BuildingSituationConvert.INSTANCE.convertList(list));
    }

    /**
     * 工情分页
     *
     * @param pageVO 查询条件 BuildingSituationPageReqVO
     * @return CommonResult<PageResult < BuildingSituationRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<BuildingSituationRespVO>> page(@RequestQueryParam BuildingSituationPageReqVO pageVO) {
        PageResult<BuildingSituationDO> pageResult = buildingSituationService.page(pageVO);
        return success(BuildingSituationConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 获取本年度工情统计
     *
     * @return 工情类型统计列表
     */
    @GetMapping("/building-stats")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<List<BuildingSituationTypeCountRespVO>> getYearStatisticsByBuilding() {
        return success(buildingSituationService.getYearStatisticsByBuilding());
    }

    /**
     * 获取本年度险工险段相关工情类型统计
     *
     * @return 工情类型统计结果
     */
    @GetMapping("/danger-stats")
    @OperateLog(module = MODULE_NAME, name = "年度险工险段相关工情统计", type = GET)
    public CommonResult<BuildingSituationDangerStatsRespVO> getYearDangerStats() {
        return success(buildingSituationService.getYearDangerStats());
    }
}