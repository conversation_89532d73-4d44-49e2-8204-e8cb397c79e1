<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.usewaterapply.UsewaterApplyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectByTime"  resultType="com.yutoudev.irrigation.irr.dal.dataobject.usewaterapply.UsewaterApplyDO">
        SELECT
            a.*
        FROM
            irr_usewater_apply a
        WHERE
            a.user_id = #{userId} and a.deleted = 0 and (
            ( a.start_time BETWEEN #{startTime} AND #{endTime} AND a.end_time &gt;= #{endTime} )
           OR ( a.end_time BETWEEN #{startTime} AND #{endTime} AND a.start_time &lt;= #{startTime} )
           OR ( a.start_time &lt;= #{startTime} AND a.end_time &gt;= #{endTime} )
           OR ( a.start_time &gt;= #{startTime} AND a.end_time &lt;= #{endTime} ) )
    </select>

    <select id="getSoilMoistureContentArea"  resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.SoilMoistureContentAreaRespVO">
        SELECT
            COALESCE ( SUM( t.area ), 0 ) area,
            COALESCE ( SUM( t.dryland_area ), 0 ) dryland_area
        FROM
            (
                SELECT COALESCE
                       ( SUM( uai.crop_area ), 0 ) area,
                       COALESCE ( ua.dryland_area, 0 ) dryland_area
                FROM
                    irr_usewater_apply ua
                        LEFT JOIN irr_usewater_apply_item uai ON uai.apply_id = ua.id
                WHERE
                    ua.`status` = 1 AND ua.deleted = 0
                  AND ua.use_type = 6
                  AND ua.dryland_area > 0
                  AND ua.start_time >= NOW() - INTERVAL 300 DAY
                GROUP BY
                    ua.id
            ) t
    </select>

    <select id="getSoilMoistureContentData"  resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.SoilMoistureContentDataRespVO">
        SELECT
            t.user_id user_id,
            t.user_name user_name,
            t.area area,
            t.dryland_area dryland_area,
            ROUND( t.dryland_area / t.area, 4 )* 100 rate,
            t.soil_moisture soil_water
        FROM
            (
                SELECT
                    ua.user_id,
                    ua.user_name,
                    COALESCE ( SUM( uai.crop_area ), 0 ) area,
                    COALESCE ( ua.dryland_area, 0 ) dryland_area,
                    COALESCE ( ua.soil_moisture, 0 ) soil_moisture,
                    ua.start_time start_time
                FROM
                    irr_usewater_apply ua
                        LEFT JOIN irr_usewater_apply_item uai ON uai.apply_id = ua.id
                WHERE
                    ua.`status` = 1 AND ua.deleted = 0
                  AND ua.use_type = 6
                  AND ua.dryland_area > 0
                GROUP BY
                    ua.id
            ) t
        ORDER BY
            t.start_time ASC
    </select>
</mapper>
