package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实时蒸散发量 Base VO
 *
 * <AUTHOR>
 * @description 实时蒸散发量 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-12-20 17:34:41
 */
@Data
public class EvaporationRealtimeBaseVO {

    /**
     * 时间
     */
    private LocalDateTime markTime;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 水面蒸发值
     */
    private Double evaporationValue;
}
