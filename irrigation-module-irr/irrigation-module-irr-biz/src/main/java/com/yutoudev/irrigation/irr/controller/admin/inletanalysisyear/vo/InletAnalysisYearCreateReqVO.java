package com.yutoudev.irrigation.irr.controller.admin.inletanalysisyear.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 年来水分析创建RequestVO
 * @description 管理后台-年来水分析创建RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:39
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisYearCreateReqVO extends InletAnalysisYearBaseVO {

}
