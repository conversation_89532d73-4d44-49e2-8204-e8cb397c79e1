package com.yutoudev.irrigation.irr.controller.admin.inletanalysismonth.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 月来水分析ResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-月来水分析ResponseVO
 * @time 2024-07-23 14:33:38
 */
@Data
@ToString(callSuper = true)
public class InletAnalysisMonthRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 集水面积ID
     */
    private Long catchmentId;
    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 月
     */
    private String month;

    /**
     * 累计降雨量
     */
    private Double rainfall;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}