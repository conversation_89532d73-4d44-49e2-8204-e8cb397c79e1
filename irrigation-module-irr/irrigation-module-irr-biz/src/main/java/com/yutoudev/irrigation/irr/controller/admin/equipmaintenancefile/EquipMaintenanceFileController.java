package com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo.*;
import com.yutoudev.irrigation.irr.convert.equipmaintenance.EquipMaintenanceFileConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipmaintenance.EquipMaintenanceFileDO;
import com.yutoudev.irrigation.irr.service.equipmaintenance.EquipMaintenanceFileService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 设备维护附件
 *
 * <AUTHOR>
 * @description 管理后台-设备维护附件controller
 * @time 2024-10-27 10:33:14
 */
@RestController
@RequestMapping("/irr/equip-maintenance-file")
@Validated
public class EquipMaintenanceFileController {

    private static final String MODULE_NAME = "设备维护附件";

    @Resource
    private EquipMaintenanceFileService<EquipMaintenanceFileDO> equipMaintenanceFileService;

    /**
     * 创建设备维护附件
     *
     * @param createReqVO EquipMaintenanceFileCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody EquipMaintenanceFileCreateReqVO createReqVO) {
        return success(equipMaintenanceFileService.create(createReqVO));
    }

    /**
     * 批量创建设备维护附件
     *
     * @param lists EquipMaintenanceFileCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<EquipMaintenanceFileCreateReqVO> lists) {
        return success(equipMaintenanceFileService.createBatch(lists));
    }

    /**
     * 更新设备维护附件
     *
     * @param updateReqVO EquipMaintenanceFileUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody EquipMaintenanceFileUpdateReqVO updateReqVO) {
        equipMaintenanceFileService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新设备维护附件
     *
     * @param lists 批量更新列表 EquipMaintenanceFileUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<EquipMaintenanceFileUpdateReqVO> lists) {
        return success(equipMaintenanceFileService.updateBatch(lists));
    }

    /**
     * 删除设备维护附件
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        equipMaintenanceFileService.delete(id);
        return success(true);
    }

    /**
     * 批量删除设备维护附件
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(equipMaintenanceFileService.deleteBatch(ids));
    }

    /**
     * 获得设备维护附件详情
     *
     * @param id 编号 Long
     * @return CommonResult<EquipMaintenanceFileDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<EquipMaintenanceFileDetailRespVO> get(@RequestParam("id") Long id) {
        EquipMaintenanceFileDO equipMaintenanceFile = equipMaintenanceFileService.get(id);
        return success(EquipMaintenanceFileConvert.INSTANCE.convertDetail(equipMaintenanceFile));
    }

    /**
     * 设备维护附件列表
     *
     * @param queryReqVO 查询条件 EquipMaintenanceFileQueryReqVO
     * @return CommonResult<List < EquipMaintenanceFileRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<EquipMaintenanceFileRespVO>> getList(@RequestQueryParam EquipMaintenanceFileQueryReqVO queryReqVO) {
        List<EquipMaintenanceFileDO> list = equipMaintenanceFileService.getList(queryReqVO);
        return success(EquipMaintenanceFileConvert.INSTANCE.convertList(list));
    }

    /**
     * 设备维护附件分页
     *
     * @param pageVO 查询条件 EquipMaintenanceFilePageReqVO
     * @return CommonResult<PageResult < EquipMaintenanceFileRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<EquipMaintenanceFileRespVO>> page(@RequestQueryParam EquipMaintenanceFilePageReqVO pageVO) {
        PageResult<EquipMaintenanceFileDO> pageResult = equipMaintenanceFileService.page(pageVO);
        return success(EquipMaintenanceFileConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出设备维护附件Excel
     *
     * @param queryReqVO 查询条件 EquipMaintenanceFileExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam EquipMaintenanceFileExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<EquipMaintenanceFileDO> list = equipMaintenanceFileService.getList(queryReqVO);
        // 导出 Excel
        List<EquipMaintenanceFileExcelVO> datas = EquipMaintenanceFileConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "设备维护附件", "xlsx"), queryReqVO.getExportSheetName(),
                EquipMaintenanceFileExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入设备维护附件模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "设备维护附件-导入模版.xls", "sheet1", EquipMaintenanceFileExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入设备维护附件Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:equip-maintenance-file:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<EquipMaintenanceFileExcelVO> list = ExcelUtils.read(file, EquipMaintenanceFileExcelVO.class);
        return success(equipMaintenanceFileService.importExcel(list, isUpdate));
    }
}