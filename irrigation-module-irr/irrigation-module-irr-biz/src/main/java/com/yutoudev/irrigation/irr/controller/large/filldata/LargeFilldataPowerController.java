package com.yutoudev.irrigation.irr.controller.large.filldata;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.filldata.vo.power.FilldataPowerPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.filldata.vo.power.FilldataPowerRespVO;
import com.yutoudev.irrigation.irr.convert.filldata.FilldataPowerConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.filldata.FilldataPowerDO;
import com.yutoudev.irrigation.irr.service.filldata.FilldataPowerService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 数据上报-发电量
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/filldata-power")
@Validated
public class LargeFilldataPowerController {

    private static final String MODULE_NAME = "数据上报-发电量";

    @Resource
    private FilldataPowerService<FilldataPowerDO> filldataPowerService;

    /**
     * 数据上报-发电量分页
     *
     * @param pageVO 查询条件 FilldataPowerPageReqVO
     * @return CommonResult<PageResult < FilldataPowerRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<FilldataPowerRespVO>> page(@RequestQueryParam FilldataPowerPageReqVO pageVO) {
        PageResult<FilldataPowerDO> pageResult = filldataPowerService.page(pageVO);
        return success(FilldataPowerConvert.INSTANCE.convertPage(pageResult));
    }

}