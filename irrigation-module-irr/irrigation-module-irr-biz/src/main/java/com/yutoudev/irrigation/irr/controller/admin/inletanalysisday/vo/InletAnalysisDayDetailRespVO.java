package com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.util.*;
import java.time.*;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;

/**
 *
 * 日来水分析DetailResponseVO
 * @description 管理后台-日来水分析DetailResponseVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:36
 *
 */
@Data
@ToString(callSuper = true)
public class InletAnalysisDayDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;
    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 日
     * 
     */
    private String day;

    /**
     * 累计降雨量
     * 
     */
    private Double rainfall;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;
}
