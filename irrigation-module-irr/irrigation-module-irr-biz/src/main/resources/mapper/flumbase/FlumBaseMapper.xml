<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.flumbase.FlumBaseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="statisticsBuilding"
            resultType="com.yutoudev.irrigation.irr.controller.large.flum.vo.BuildingStatsRespVO">
        SELECT
            t.*,
            ROUND((t.typeACount + t.typeBCount) * 100.0 / NULLIF(t.totalCount, 0), 2) AS intactRate
        FROM (
             SELECT
                 COUNT(*) AS totalCount,
                 COUNT(CASE WHEN situation_type = 0 THEN 1 END) AS typeACount,
                 COUNT(CASE WHEN situation_type = 1 THEN 1 END) AS typeBCount,
                 COUNT(CASE WHEN situation_type = 2 THEN 1 END) AS typeCCount,
                 COUNT(CASE WHEN situation_type = 3 THEN 1 END) AS typeDCount
             FROM irr_flum_base
             WHERE deleted = 0
         ) t
    </select>

</mapper>
