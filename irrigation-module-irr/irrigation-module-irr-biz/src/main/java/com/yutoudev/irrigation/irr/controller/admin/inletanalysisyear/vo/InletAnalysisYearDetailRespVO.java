package com.yutoudev.irrigation.irr.controller.admin.inletanalysisyear.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 年来水分析DetailResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-年来水分析DetailResponseVO
 * @time 2024-07-23 14:33:39
 */
@Data
@ToString(callSuper = true)
public class InletAnalysisYearDetailRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 集水面积ID
     */
    private Long catchmentId;
    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 年
     */
    private String year;

    /**
     * 累计降雨量
     */
    private Double rainfall;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;
}
