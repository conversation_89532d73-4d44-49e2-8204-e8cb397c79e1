package com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelQueryReqVo;
import com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelRespVo;
import com.yutoudev.irrigation.irr.service.inletinversionmodel.InletInversionModelService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 *
 * 引水量反演模型
 * @description 管理后台-引水量反演模型controller
 * <AUTHOR>
 * @time 2024-06-21 10:16:05
 *
 */
@RestController
@RequestMapping("/irr/inlet-inversion-model")
@Validated
public class InletInversionModelController {

    private static final String MODULE_NAME = "引水量反演模型";

    @Resource
    private InletInversionModelService inletInversionModelService;


    /**
     * 引水量反演模型数据水源列表查询
     * @description
     * @param queryReqVO 查询条件 InletInversionModelQueryReqVo
     * @return CommonResult<List<InletInversionModelRespVo>> 列表响应VO
     */
    @GetMapping("/swhsList")
    @PreAuthorize("@ss.hasPermission('irr:inlet-inversion-model:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<InletInversionModelRespVo>> getSwhsList(@RequestQueryParam InletInversionModelQueryReqVo queryReqVO) {
        List<InletInversionModelRespVo> list = inletInversionModelService.getSwhsList(queryReqVO);
        return success(list);
    }

    /**
     * 引水量反演模型数据水源列表查询
     * @description
     * @param queryReqVO 查询条件 InletInversionModelQueryReqVo
     * @return CommonResult<List<InletInversionModelRespVo>> 列表响应VO
     */
    @GetMapping("/chanList")
    @PreAuthorize("@ss.hasPermission('irr:inlet-inversion-model:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<InletInversionModelRespVo>> getChanList(@RequestQueryParam InletInversionModelQueryReqVo queryReqVO) {
        List<InletInversionModelRespVo> list = inletInversionModelService.getChanList(queryReqVO);
        return success(list);
    }

}