package com.yutoudev.irrigation.irr.controller.admin.inletanalysisyear.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisyear.InletAnalysisYearDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 *
 * 年来水分析分页RequestVO
 * @description 管理后台-年来水分析分页RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:39
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisYearPageReqVO extends PageCriteria<InletAnalysisYearDO> {

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 年
     * 
     */
    private String year;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;

}
