package com.yutoudev.irrigation.irr.controller.large.rainbriefing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 雨情简报ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RainBriefingLargeRespVO {


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布日期
     */
    private LocalDate publishDate;

    /**
     * 信息来源
     */
    private String source;

    /**
     * 附件
     */
    private String files;

    /**
     * 创建人
     */
    private String creator;

}