package com.yutoudev.irrigation.irr.controller.large.archive;


import com.yutoudev.irrigation.archive.api.ArchiveLargeApi;
import com.yutoudev.irrigation.archive.api.dto.ArchivesLargeInfoDTO;
import com.yutoudev.irrigation.archive.api.dto.ArchivesLargeInfoPageReqDTO;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

/**
 * 档案大屏接口
 */
@RestController
@RequestMapping("/irr/large-archive")
@Validated
public class ArchiveLargeController {

    @Resource
    private ArchiveLargeApi archiveLargeApi;

    @GetMapping("/total")
    public CommonResult<Long> getTotal(){
        return CommonResult.success(archiveLargeApi.getTotal());
    }

    @GetMapping("/page")
    public CommonResult<PageResult<ArchivesLargeInfoDTO>> page(@RequestQueryParam ArchivesLargeInfoPageReqDTO pageVO) {
        PageResult<ArchivesLargeInfoDTO> pageResult = archiveLargeApi.page(pageVO);
        return success(pageResult);
    }

}
