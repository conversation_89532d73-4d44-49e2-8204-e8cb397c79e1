package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 实时蒸散发量创建RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-实时蒸散发量创建RequestVO
 * @time 2024-12-20 17:34:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvaporationRealtimeCreateReqVO extends EvaporationRealtimeBaseVO {

}
