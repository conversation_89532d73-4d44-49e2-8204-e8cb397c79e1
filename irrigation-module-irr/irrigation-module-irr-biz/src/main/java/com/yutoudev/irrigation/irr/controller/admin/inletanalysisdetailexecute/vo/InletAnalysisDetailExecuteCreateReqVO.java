package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 来水分析明细执行记录创建RequestVO
 * @description 管理后台-来水分析明细执行记录创建RequestVO
 * <AUTHOR>
 * @time 2024-08-09 10:49:53
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisDetailExecuteCreateReqVO extends InletAnalysisDetailExecuteBaseVO {

}
