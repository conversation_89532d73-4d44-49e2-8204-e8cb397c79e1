<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.allocationwater.AllocationWaterSchemeGateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectGropyByEquipId" resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterSchedulingRespVO">

        SELECT
            g.equip_id AS id,
            eb.st_name AS `name`,
            eb.dpds_num,
            g.water,
            g.flow_rate,
            g.gate_height,
            g.gate_time AS gateTime
        FROM
            `irr_allocation_water_scheme_gate` g
                JOIN (
                SELECT
                    equip_id,
                    MIN(gate_time) AS min_gate_time
                FROM
                    `irr_allocation_water_scheme_gate`
                WHERE
                    gate_time > NOW()
                    <if test="gateId != null" >
                        and gate_id = #{gateId}
                    </if>
                    <if test="equipId != null" >
                        and equip_id = #{equipId}
                    </if>
                GROUP BY
                    equip_id
            ) sub ON g.equip_id = sub.equip_id AND g.gate_time = sub.min_gate_time
                LEFT JOIN
            irr_equip_base eb ON g.equip_id = eb.id
        ORDER BY
            g.gate_time ASC
    </select>

    <select id="selectByEquipId" resultType="com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterSchedulingRespVO">
        SELECT
        g.equip_id id,
        eb.st_name `name`,
        eb.dpds_num,
        g.water,
        g.flow_rate,
        g.gate_height,
        g.gate_time gateTime
        FROM
        ( SELECT * FROM `irr_allocation_water_scheme_gate` WHERE gate_time > NOW() AND equip_id = #{equipId} ORDER BY gate_time ASC ) g
        LEFT JOIN irr_equip_base eb ON g.equip_id = eb.id
        ORDER BY g.gate_time ASC
    </select>

    <delete id="cleanGate">
        DELETE FROM `irr_allocation_water_scheme_gate`
        WHERE (`gate_time`, `gate_height`) NOT IN (
            SELECT `start_time`, `gate_height` FROM `irr_allocation_water_scheme`
            WHERE `irr_allocation_water_scheme_gate`.`equip_id` = `irr_allocation_water_scheme`.`equip_id`
        )
          AND (`gate_time`, `gate_height`) NOT IN (
            SELECT `end_time`, `gate_default_height` FROM `irr_allocation_water_scheme`
            WHERE `irr_allocation_water_scheme_gate`.`equip_id` = `irr_allocation_water_scheme`.`equip_id`
        )
    </delete>

    <select id="selectLessThanGateTimeByEquipId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.allocationwater.AllocationWaterSchemeGateDO">
        SELECT * from `irr_allocation_water_scheme_gate` where equip_id = #{equipId} and gate_time &lt; #{gateTime}  order by gate_time desc limit 1
    </select>
    <select id="selectGreaterThanGateTimeByEquipId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.allocationwater.AllocationWaterSchemeGateDO">
        SELECT * from `irr_allocation_water_scheme_gate` where equip_id = #{equipId} and gate_time &gt; #{gateTime}  order by gate_time asc limit 1
    </select>
</mapper>
