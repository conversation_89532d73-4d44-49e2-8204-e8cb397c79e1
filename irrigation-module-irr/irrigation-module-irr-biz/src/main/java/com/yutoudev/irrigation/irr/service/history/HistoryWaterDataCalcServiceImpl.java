package com.yutoudev.irrigation.irr.service.history;

import com.yutoudev.irrigation.framework.common.util.object.ReflectionUtils;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipcalcconfig.EquipCalcConfigDO;
import com.yutoudev.irrigation.irr.dal.dataobject.history.*;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.enums.EquipDataCalcFrequencyEnum;
import com.yutoudev.irrigation.irr.enums.HistoryAvgTypeEnum;
import com.yutoudev.irrigation.irr.enums.StatisticalFrequencyEnum;
import com.yutoudev.irrigation.irr.service.equipcalcconfig.EquipCalcConfigService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndHourService;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsBaseService;
import com.yutoudev.irrigation.irr.service.waterdatacalc.ReservoirDataCalcService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;
import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;
import static com.yutoudev.irrigation.irr.enums.HistoryGlobalConstants.HISTORY_TABLE_FIELD_MONTH_ABBR;
import static com.yutoudev.irrigation.irr.enums.IrrigationGlobalConstants.SYSTEM_CREATOR;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HistoryWaterDataCalcServiceImpl implements HistoryWaterDataCalcService {

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Resource
    private HistoryInflowDayService<HistoryInflowDayDO> historyInflowDayService;

    @Resource
    private HistoryInflowService<HistoryInflowDO> historyInflowService;

    @Resource
    private HistoryCapacityDayService<HistoryCapacityDayDO> historyCapacityDayService;

    @Resource
    private HistoryCapacityService<HistoryCapacityDO> historyCapacityService;

    @Resource
    private HistoryAvgDayService<HistoryAvgDayDO> historyAvgDayService;

    @Resource
    private HistoryAvgService<HistoryAvgDO> historyAvgService;

    @Resource
    private SwhsBaseService<SwhsBaseDO> swhsBaseService;

    @Resource
    private EquipCalcConfigService<EquipCalcConfigDO> equipCalcConfigService;

    @Resource
    private ReservoirDataCalcService reservoirDataCalcService;

    /**
     * 获取昨天的日期时间信息
     */
    private DateTimeInfo getYesterdayDateTimeInfo() {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        LocalDate yesterdayDate = LocalDate.now().minusDays(1);

        return DateTimeInfo.builder()
                .date(yesterdayDate)
                .dateTime(yesterday)
                .monthFormat(yesterday.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_SHORT)))
                .monthName(yesterday.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_ZH)))
                .yearFormat(yesterday.format(DateTimeFormatter.ofPattern(FORMAT_YEAR)))
                .yearName(yesterday.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_ZH)))
                .dayFormat(yesterdayDate.format(DateTimeFormatter.ofPattern(FORMAT_MONTH_DAY_SHORT)))
                .build();
    }

    /**
     * 保存或更新历史数据的通用方法
     */
    private <T> void saveOrUpdate(T entity, java.util.function.Supplier<T> querySupplier,
                                  java.util.function.Consumer<T> saveAction,
                                  java.util.function.Consumer<T> updateAction) {
        T existing = querySupplier.get();
        if (Objects.isNull(existing) || (existing instanceof HistoryInflowDO && Objects.isNull(((HistoryInflowDO) existing).getId()))
                || (existing instanceof HistoryCapacityDO && Objects.isNull(((HistoryCapacityDO) existing).getId()))) {
            saveAction.accept(entity);
        } else {
            updateAction.accept(entity);
        }
    }

    /**
     * 日期时间信息封装类
     */
    private static class DateTimeInfo {
        private final LocalDate date;
        private final LocalDateTime dateTime;
        private final String monthFormat;
        private final String monthName;
        private final String yearFormat;
        private final String yearName;
        private final String dayFormat;

        private DateTimeInfo(LocalDate date, LocalDateTime dateTime, String monthFormat,
                           String monthName, String yearFormat, String yearName, String dayFormat) {
            this.date = date;
            this.dateTime = dateTime;
            this.monthFormat = monthFormat;
            this.monthName = monthName;
            this.yearFormat = yearFormat;
            this.yearName = yearName;
            this.dayFormat = dayFormat;
        }

        public static DateTimeInfoBuilder builder() {
            return new DateTimeInfoBuilder();
        }

        public static class DateTimeInfoBuilder {
            private LocalDate date;
            private LocalDateTime dateTime;
            private String monthFormat;
            private String monthName;
            private String yearFormat;
            private String yearName;
            private String dayFormat;

            public DateTimeInfoBuilder date(LocalDate date) { this.date = date; return this; }
            public DateTimeInfoBuilder dateTime(LocalDateTime dateTime) { this.dateTime = dateTime; return this; }
            public DateTimeInfoBuilder monthFormat(String monthFormat) { this.monthFormat = monthFormat; return this; }
            public DateTimeInfoBuilder monthName(String monthName) { this.monthName = monthName; return this; }
            public DateTimeInfoBuilder yearFormat(String yearFormat) { this.yearFormat = yearFormat; return this; }
            public DateTimeInfoBuilder yearName(String yearName) { this.yearName = yearName; return this; }
            public DateTimeInfoBuilder dayFormat(String dayFormat) { this.dayFormat = dayFormat; return this; }

            public DateTimeInfo build() {
                return new DateTimeInfo(date, dateTime, monthFormat, monthName, yearFormat, yearName, dayFormat);
            }
        }

        // Getters
        public LocalDate getDate() { return date; }
        public LocalDateTime getDateTime() { return dateTime; }
        public String getMonthFormat() { return monthFormat; }
        public String getMonthName() { return monthName; }
        public String getYearFormat() { return yearFormat; }
        public String getYearName() { return yearName; }
        public String getDayFormat() { return dayFormat; }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHistoryWaterData() {
        log.info("开始保存历史水数据");
        try {
            // 暂取主水源
            EquipBaseCacheVO equip = reservoirDataCalcService.getMainWaterSourceEquip();

            Long mainReservoirId = equipCalcConfigService.getCacheMainWaterSourceConfig().getWaterSourceId();
            SwhsBaseDO waterSource = swhsBaseService.getById(mainReservoirId);

            LocalDateTime beginTime = LocalDateTime.now().minusDays(1).withHour(8).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endTime = LocalDateTime.now().withHour(8).withMinute(0).withMinute(0).withNano(0);

            HashMap<String, Double> dataMap = new HashMap<>();

            // 历史来水
            saveInflowWaterData(dataMap, waterSource, equip, beginTime, endTime);

            // 历史库容
            saveCapacityWaterData(dataMap, waterSource, equip, beginTime);

            // 历史均值
            saveAvgData(dataMap, waterSource);

            log.info("历史水数据保存完成");
        } catch (Exception e) {
            log.error("保存历史水数据失败", e);
            throw e;
        }
    }

    /**
     * 写入来水数据
     *
     * @param dataMap 数据缓存
     * @param waterSource 水源
     * @param equip 水源设备
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    @Transactional(rollbackFor = Exception.class)
    private void saveInflowWaterData(HashMap<String, Double> dataMap,
                                     SwhsBaseDO waterSource,
                                     EquipBaseCacheVO equip,
                                     LocalDateTime beginTime,
                                     LocalDateTime endTime) {
        log.debug("开始保存来水数据，水源ID: {}", waterSource.getId());

        DateTimeInfo dateTimeInfo = getYesterdayDateTimeInfo();

        // 日来水数据
        List<MeasIndHourDO> dayList = measIndHourService.getListByEquipAndDateRange(equip, beginTime, endTime);
        double inflowVolume = dayList.isEmpty() ? 0
                : dayList.stream().mapToDouble(MeasIndHourDO::getReservoirInflowVolume).sum();
        dataMap.put("inflowVolume", inflowVolume);

        // 保存日来水数据
        HistoryInflowDayDO historyInflowDay = createHistoryInflowDay(waterSource, inflowVolume, dateTimeInfo.getDate());
        historyInflowDayService.save(historyInflowDay);

        // 保存月来水数据
        HistoryInflowDO historyMonth = assembleHistoryInflow(inflowVolume, waterSource,
                dateTimeInfo.getMonthFormat(), dateTimeInfo.getMonthName(), StatisticalFrequencyEnum.MONTH.getValue());
        saveOrUpdate(historyMonth,
                () -> historyInflowService.getByWaterSourceAndFrequency(waterSource.getId(),
                        StatisticalFrequencyEnum.MONTH.getValue(), Long.parseLong(dateTimeInfo.getMonthFormat())),
                historyInflowService::save,
                historyInflowService::updateById);

        // 保存年来水数据
        HistoryInflowDO historyYear = assembleHistoryInflow(inflowVolume, waterSource,
                dateTimeInfo.getYearFormat(), dateTimeInfo.getYearName(), StatisticalFrequencyEnum.YEAR.getValue());
        saveOrUpdate(historyYear,
                () -> historyInflowService.getByWaterSourceAndFrequency(waterSource.getId(),
                        StatisticalFrequencyEnum.YEAR.getValue(), Long.parseLong(dateTimeInfo.getYearFormat())),
                historyInflowService::save,
                historyInflowService::updateById);

        log.debug("来水数据保存完成，来水量: {}", inflowVolume);
    }

    /**
     * 创建历史来水日数据
     */
    private HistoryInflowDayDO createHistoryInflowDay(SwhsBaseDO waterSource, double inflowVolume, LocalDate markDay) {
        HistoryInflowDayDO historyInflowDay = new HistoryInflowDayDO();
        historyInflowDay.setMarkDay(markDay);
        historyInflowDay.setSwhsId(waterSource.getId());
        historyInflowDay.setSwhsName(waterSource.getSwhsName());
        historyInflowDay.setInflow(inflowVolume);
        return historyInflowDay;
    }

    private HistoryInflowDO assembleHistoryInflow(double inflowVolume,
                                                  SwhsBaseDO waterSource,
                                                  String format,
                                                  String name,
                                                  int frequency) {
        HistoryInflowDO historyInflow = historyInflowService.getByWaterSourceAndFrequency(waterSource.getId(), frequency,
                Long.parseLong(format));
        LocalDate yesterday = LocalDate.now().minusDays(1);

        if (Objects.isNull(historyInflow)) {
            historyInflow = createNewHistoryInflow(waterSource, inflowVolume, format, name, frequency, yesterday);
        } else {
            updateExistingHistoryInflow(historyInflow, inflowVolume, yesterday);
        }

        historyInflow.setLister(SYSTEM_CREATOR);
        historyInflow.setMarkTime(LocalDateTime.now());
        return historyInflow;
    }

    /**
     * 创建新的历史来水记录
     */
    private HistoryInflowDO createNewHistoryInflow(SwhsBaseDO waterSource, double inflowVolume,
                                                   String format, String name, int frequency, LocalDate yesterday) {
        HistoryInflowDO historyInflow = new HistoryInflowDO();
        historyInflow.setName(name);
        historyInflow.setCode(Long.parseLong(format));
        historyInflow.setFrequency(frequency);
        historyInflow.setSwhsId(waterSource.getId());
        historyInflow.setSwhsName(waterSource.getSwhsName());
        historyInflow.setTotal(inflowVolume);
        historyInflow.setMaxDay(yesterday);
        historyInflow.setMaxVolume(inflowVolume);
        historyInflow.setMinDay(yesterday);
        historyInflow.setMinVolume(inflowVolume);
        historyInflow.setAvgVolume(inflowVolume);
        return historyInflow;
    }

    /**
     * 更新现有的历史来水记录
     */
    private void updateExistingHistoryInflow(HistoryInflowDO historyInflow, double inflowVolume, LocalDate yesterday) {
        historyInflow.setTotal(historyInflow.getTotal() + inflowVolume);
        if (inflowVolume > historyInflow.getMaxVolume()) {
            historyInflow.setMaxVolume(inflowVolume);
            historyInflow.setMaxDay(yesterday);
        }
        if (inflowVolume < historyInflow.getMinVolume()) {
            historyInflow.setMinVolume(inflowVolume);
            historyInflow.setMinDay(yesterday);
        }
        double avg = convert((historyInflow.getAvgVolume() + inflowVolume) / 2, 1);
        historyInflow.setAvgVolume(avg);
    }

    @Transactional(rollbackFor = Exception.class)
    private void saveCapacityWaterData(Map<String, Double> dataMap,
                                       SwhsBaseDO waterSource,
                                       EquipBaseCacheVO equip,
                                       LocalDateTime beginTime) {
        log.debug("开始保存库容数据，水源ID: {}", waterSource.getId());

        DateTimeInfo dateTimeInfo = getYesterdayDateTimeInfo();
        LocalDate beginDate = beginTime.toLocalDate();

        // 计算并保存日库容量
        double storageCapacity = reservoirDataCalcService.getReservoirCapacityByHistory(
                equip.getCalcResourceId(), beginDate, EquipDataCalcFrequencyEnum.DAY.getValue());
        dataMap.put("storageCapacity", storageCapacity);
        HistoryCapacityDayDO historyCapacityDay = createHistoryCapacityDay(storageCapacity, waterSource, dateTimeInfo.getDate());
        historyCapacityDayService.save(historyCapacityDay);

        // 计算并保存月库容量
        double monthStorageCapacity = reservoirDataCalcService.getReservoirCapacityByHistory(
                equip.getCalcResourceId(), beginDate, EquipDataCalcFrequencyEnum.MONTH.getValue());
        HistoryCapacityDO historyCapacityMonth = assembleHistoryCapacity(monthStorageCapacity, waterSource,
                dateTimeInfo.getMonthFormat(), dateTimeInfo.getMonthName(), EquipDataCalcFrequencyEnum.MONTH.getValue());
        saveOrUpdate(historyCapacityMonth,
                () -> historyCapacityService.getByWaterSourceAndFrequency(waterSource.getId(),
                        EquipDataCalcFrequencyEnum.MONTH.getValue(), Long.parseLong(dateTimeInfo.getMonthFormat())),
                historyCapacityService::save,
                historyCapacityService::updateById);

        // 计算并保存年库容量
        double yearStorageCapacity = reservoirDataCalcService.getReservoirCapacityByHistory(
                equip.getCalcResourceId(), beginDate, EquipDataCalcFrequencyEnum.YEAR.getValue());
        HistoryCapacityDO historyCapacityYear = assembleHistoryCapacity(yearStorageCapacity, waterSource,
                dateTimeInfo.getYearFormat(), dateTimeInfo.getYearName(), EquipDataCalcFrequencyEnum.YEAR.getValue());
        saveOrUpdate(historyCapacityYear,
                () -> historyCapacityService.getByWaterSourceAndFrequency(waterSource.getId(),
                        EquipDataCalcFrequencyEnum.YEAR.getValue(), Long.parseLong(dateTimeInfo.getYearFormat())),
                historyCapacityService::save,
                historyCapacityService::updateById);

        log.debug("库容数据保存完成，日库容: {}, 月库容: {}, 年库容: {}",
                storageCapacity, monthStorageCapacity, yearStorageCapacity);
    }

    /**
     * 创建历史库容日数据
     */
    private HistoryCapacityDayDO createHistoryCapacityDay(double storageCapacity, SwhsBaseDO waterSource, LocalDate markDay) {
        HistoryCapacityDayDO historyCapacityDay = new HistoryCapacityDayDO();
        historyCapacityDay.setMarkDay(markDay);
        historyCapacityDay.setSwhsId(waterSource.getId());
        historyCapacityDay.setSwhsName(waterSource.getSwhsName());
        historyCapacityDay.setCapacity(storageCapacity);
        return historyCapacityDay;
    }

    private HistoryCapacityDO assembleHistoryCapacity(double storageCapacity, SwhsBaseDO waterSource, String format,
                                                      String name, int frequency) {
        HistoryCapacityDO historyCapacity = historyCapacityService.getByWaterSourceAndFrequency(waterSource.getId(), frequency,
                Long.parseLong(format));
        LocalDate yesterday = LocalDate.now().minusDays(1);

        if (Objects.isNull(historyCapacity)) {
            historyCapacity = createNewHistoryCapacity(waterSource, storageCapacity, format, name, frequency, yesterday);
        } else {
            updateExistingHistoryCapacity(historyCapacity, storageCapacity, yesterday);
        }

        historyCapacity.setLister(SYSTEM_CREATOR);
        historyCapacity.setMarkTime(LocalDateTime.now());
        return historyCapacity;
    }

    /**
     * 创建新的历史库容记录
     */
    private HistoryCapacityDO createNewHistoryCapacity(SwhsBaseDO waterSource, double storageCapacity,
                                                       String format, String name, int frequency, LocalDate yesterday) {
        HistoryCapacityDO historyCapacity = new HistoryCapacityDO();
        historyCapacity.setName(name);
        historyCapacity.setCode(Long.parseLong(format));
        historyCapacity.setFrequency(frequency);
        historyCapacity.setSwhsId(waterSource.getId());
        historyCapacity.setSwhsName(waterSource.getSwhsName());
        historyCapacity.setTotal(storageCapacity);
        historyCapacity.setMaxDay(yesterday);
        historyCapacity.setMaxVolume(storageCapacity);
        historyCapacity.setMinDay(yesterday);
        historyCapacity.setMinVolume(storageCapacity);
        historyCapacity.setAvgVolume(storageCapacity);
        return historyCapacity;
    }

    /**
     * 更新现有的历史库容记录
     */
    private void updateExistingHistoryCapacity(HistoryCapacityDO historyCapacity, double storageCapacity, LocalDate yesterday) {
        historyCapacity.setTotal(storageCapacity);
        if (storageCapacity > historyCapacity.getMaxVolume()) {
            historyCapacity.setMaxVolume(storageCapacity);
            historyCapacity.setMaxDay(yesterday);
        }
        if (storageCapacity < historyCapacity.getMinVolume()) {
            historyCapacity.setMinVolume(storageCapacity);
            historyCapacity.setMinDay(yesterday);
        }
        double avg = convert((historyCapacity.getAvgVolume() + storageCapacity) / 2, 1);
        historyCapacity.setAvgVolume(avg);
    }

    @Transactional(rollbackFor = Exception.class)
    private void saveAvgData(HashMap<String, Double> dataMap, SwhsBaseDO waterSource) {
        log.debug("开始保存均值数据，水源ID: {}", waterSource.getId());

        double inflowVolume = dataMap.get("inflowVolume");
        double storageCapacity = dataMap.get("storageCapacity");

        // 计算日均值
        List<HistoryAvgDayDO> historyAvgDayList = assembleHistoryAvgDay(waterSource, inflowVolume, storageCapacity);
        if (!historyAvgDayList.isEmpty()) {
            historyAvgDayService.updateBatchById(historyAvgDayList);
        }

        // 计算月均值
        List<HistoryAvgDO> historyAvgMonthList = assembleHistoryAvgMonth(waterSource, inflowVolume, storageCapacity);
        if (!historyAvgMonthList.isEmpty()) {
            historyAvgService.updateBatchById(historyAvgMonthList);
        }

        log.debug("均值数据保存完成");
    }

    private List<HistoryAvgDayDO> assembleHistoryAvgDay(SwhsBaseDO waterSource, double inflowVolume, double storageCapacity) {
        List<HistoryAvgDayDO> list = new ArrayList<>();
        DateTimeInfo dateTimeInfo = getYesterdayDateTimeInfo();
        int dayFormat = Integer.parseInt(dateTimeInfo.getDayFormat());

        // 处理来水均值
        HistoryAvgDayDO historyInflowVolumeAvgDay = historyAvgDayService.getByWaterSourceAndTypeAndDay(
                waterSource.getId(), HistoryAvgTypeEnum.INFLOW.getType(), dayFormat);
        if (Objects.nonNull(historyInflowVolumeAvgDay)) {
            double inflowVolumeAvg = convert((historyInflowVolumeAvgDay.getAvgValue() + inflowVolume) / 2, 1);
            historyInflowVolumeAvgDay.setAvgValue(inflowVolumeAvg);
            list.add(historyInflowVolumeAvgDay);
        }

        // 处理库容均值
        HistoryAvgDayDO historyStorageCapacityAvgDay = historyAvgDayService.getByWaterSourceAndTypeAndDay(
                waterSource.getId(), HistoryAvgTypeEnum.CAPACITY.getType(), dayFormat);
        if (Objects.nonNull(historyStorageCapacityAvgDay)) {
            double storageCapacityAvg = convert((historyStorageCapacityAvgDay.getAvgValue() + storageCapacity) / 2, 1);
            historyStorageCapacityAvgDay.setAvgValue(storageCapacityAvg);
            list.add(historyStorageCapacityAvgDay);
        }

        return list;
    }

    private List<HistoryAvgDO> assembleHistoryAvgMonth(SwhsBaseDO waterSource, double inflowVolume, double storageCapacity) {
        List<HistoryAvgDO> list = new ArrayList<>();
        DateTimeInfo dateTimeInfo = getYesterdayDateTimeInfo();
        int month = dateTimeInfo.getDate().getMonthValue();

        // 处理来水月均值
        HistoryAvgDO historyInflowVolumeAvgMonth = historyAvgService.getByWaterSourceAndType(
                waterSource.getId(), HistoryAvgTypeEnum.INFLOW.getType());
        if (Objects.nonNull(historyInflowVolumeAvgMonth)) {
            updateMonthlyAvg(historyInflowVolumeAvgMonth, month, inflowVolume);
            list.add(historyInflowVolumeAvgMonth);
        }

        // 处理库容月均值
        HistoryAvgDO historyStorageCapacityAvgMonth = historyAvgService.getByWaterSourceAndType(
                waterSource.getId(), HistoryAvgTypeEnum.CAPACITY.getType());
        if (Objects.nonNull(historyStorageCapacityAvgMonth)) {
            updateMonthlyAvg(historyStorageCapacityAvgMonth, month, storageCapacity);
            list.add(historyStorageCapacityAvgMonth);
        }

        return list;
    }

    /**
     * 更新月度均值数据
     */
    private void updateMonthlyAvg(HistoryAvgDO historyAvg, int month, double newValue) {
        String monthField = HISTORY_TABLE_FIELD_MONTH_ABBR + month;
        double currentMonthValue = (double) ReflectionUtils.getFieldValue(historyAvg, monthField);
        double monthTotal = historyAvg.getTotal() - currentMonthValue;
        double newMonthAvg = convert((currentMonthValue + newValue) / 2, 1);

        ReflectionUtils.setFieldValue(historyAvg, monthField, newMonthAvg);
        historyAvg.setTotal(monthTotal + newMonthAvg);
    }
}
