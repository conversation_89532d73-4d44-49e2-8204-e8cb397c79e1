package com.yutoudev.irrigation.irr.controller.large.regimen;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.history.vo.avg.HistoryAvgRespVO;
import com.yutoudev.irrigation.irr.convert.history.HistoryAvgConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryAvgDO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.enums.HistoryAvgTypeEnum;
import com.yutoudev.irrigation.irr.service.history.HistoryAvgService;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsBaseService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/year-inflow")
@Validated
public class LargeYearInflowPredictionController {

    private static final String MODULE_NAME = "大屏-年来水分析";

    @Resource
    private SwhsBaseService<SwhsBaseDO> swhsBaseService;

    @Resource
    private HistoryAvgService<HistoryAvgDO> historyAvgService;

    @GetMapping("/get-prediction")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<HistoryAvgRespVO> getPrediction() {
        Long mainReservoirId = swhsBaseService.getMainReservoirId();
        return success(HistoryAvgConvert.INSTANCE.convert(historyAvgService.getByWaterSourceAndType(mainReservoirId, HistoryAvgTypeEnum.INFLOW.getType())));
    }
}
