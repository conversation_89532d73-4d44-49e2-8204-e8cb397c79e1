package com.yutoudev.irrigation.irr.controller.admin.rainfallprocess.vo.runoffitem;

import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.RainfallProcessRunoffItemDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 流域降雨过程净雨list查询RequestVO
 *
 * <AUTHOR>
 */
@Data
public class RainfallProcessRunoffItemQueryReqVO extends QueryCriteria<RainfallProcessRunoffItemDO> {

    /**
     * ID
     */
    private Long id;

    /**
     * 流域降雨过程ID
     */
    private Long processId;

    /**
     * 时间
     */
    private LocalDateTime reportTime;

}
