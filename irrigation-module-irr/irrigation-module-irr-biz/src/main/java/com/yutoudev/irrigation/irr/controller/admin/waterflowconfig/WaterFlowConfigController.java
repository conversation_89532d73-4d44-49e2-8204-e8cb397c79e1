package com.yutoudev.irrigation.irr.controller.admin.waterflowconfig;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.config.*;
import com.yutoudev.irrigation.irr.convert.waterflowconfig.WaterFlowConfigConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.waterflowconfig.WaterFlowConfigDO;
import com.yutoudev.irrigation.irr.service.waterflowconfig.WaterFlowConfigService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 *
 * 水流配置
 * @description 管理后台-水流配置controller
 * <AUTHOR>
 * @time 2024-08-13 15:15:33
 *
 */
@RestController
@RequestMapping("/irr/water-flow-config")
@Validated
public class WaterFlowConfigController {

    private static final String MODULE_NAME = "水流配置";

    @Resource
    private WaterFlowConfigService<WaterFlowConfigDO> waterFlowConfigService;

    /**
     * 创建水流配置
     * @description 单个对象保存
     * @param createReqVO WaterFlowConfigCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody WaterFlowConfigCreateReqVO createReqVO) {
        return success(waterFlowConfigService.create(createReqVO));
    }

    /**
     * 批量创建水流配置
     * @description 多个对象保存
     * @param lists  WaterFlowConfigCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<WaterFlowConfigCreateReqVO> lists) {
        return success(waterFlowConfigService.createBatch(lists));
    }

    /**
     * 更新水流配置
     * @description 单个对象修改
     * @param updateReqVO WaterFlowConfigUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody WaterFlowConfigUpdateReqVO updateReqVO) {
        waterFlowConfigService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新水流配置
     * @description 批量更新
     * @param lists 批量更新列表 WaterFlowConfigUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<WaterFlowConfigUpdateReqVO> lists) {
        return success(waterFlowConfigService.updateBatch(lists));
    }

    /**
     * 删除水流配置
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        waterFlowConfigService.delete(id);
        return success(true);
    }

    /**
     * 批量删除水流配置
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(waterFlowConfigService.deleteBatch(ids));
    }

    /**
     * 获得水流配置详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<WaterFlowConfigDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<WaterFlowConfigDetailRespVO> get(@RequestParam("id") Long id) {
        WaterFlowConfigDO waterFlowConfig = waterFlowConfigService.get(id);
        return success(WaterFlowConfigConvert.INSTANCE.convertDetail(waterFlowConfig));
    }

    /**
     * 水流配置列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 WaterFlowConfigQueryReqVO
     * @return CommonResult<List<WaterFlowConfigRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<WaterFlowConfigRespVO>> getList(@RequestQueryParam WaterFlowConfigQueryReqVO queryReqVO) {
        List<WaterFlowConfigDO> list = waterFlowConfigService.getList(queryReqVO);
        return success(WaterFlowConfigConvert.INSTANCE.convertList(list));
    }

    /**
     * 水流配置分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 WaterFlowConfigPageReqVO
     * @return CommonResult<PageResult<WaterFlowConfigRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<WaterFlowConfigRespVO>> page(@RequestQueryParam WaterFlowConfigPageReqVO pageVO) {
        PageResult<WaterFlowConfigDO> pageResult = waterFlowConfigService.page(pageVO);
        return success(WaterFlowConfigConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出水流配置Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 WaterFlowConfigExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam WaterFlowConfigExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<WaterFlowConfigDO> list = waterFlowConfigService.getList(queryReqVO);
        // 导出 Excel
        List<WaterFlowConfigExcelVO> datas = WaterFlowConfigConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "水流配置", "xlsx"), queryReqVO.getExportSheetName(),
                                                    WaterFlowConfigExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入水流配置模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "水流配置-导入模版.xls", "sheet1", WaterFlowConfigExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入水流配置Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<WaterFlowConfigExcelVO> list = ExcelUtils.read(file, WaterFlowConfigExcelVO.class);
        return success(waterFlowConfigService.importExcel(list, isUpdate));
    }
}