package com.yutoudev.irrigation.irr.controller.admin.waterflowconfig;

import com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.info.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.validation.*;
import java.util.*;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;


import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yutoudev.irrigation.irr.dal.dataobject.waterflowconfig.WaterFlowConfigInfoDO;
import com.yutoudev.irrigation.irr.convert.waterflowconfig.WaterFlowConfigInfoConvert;
import com.yutoudev.irrigation.irr.service.waterflowconfig.WaterFlowConfigInfoService;


/**
 *
 * 水流配置信息
 * @description 管理后台-水流配置信息controller
 * <AUTHOR>
 * @time 2024-08-13 15:15:34
 *
 */
@RestController
@RequestMapping("/irr/water-flow-config-info")
@Validated
public class WaterFlowConfigInfoController {

    private static final String MODULE_NAME = "水流配置信息";

    @Resource
    private WaterFlowConfigInfoService<WaterFlowConfigInfoDO> waterFlowConfigInfoService;

    /**
     * 创建水流配置信息
     * @description 单个对象保存
     * @param createReqVO WaterFlowConfigInfoCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody WaterFlowConfigInfoCreateReqVO createReqVO) {
        return success(waterFlowConfigInfoService.create(createReqVO));
    }

    /**
     * 批量创建水流配置信息
     * @description 多个对象保存
     * @param lists  WaterFlowConfigInfoCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<WaterFlowConfigInfoCreateReqVO> lists) {
        return success(waterFlowConfigInfoService.createBatch(lists));
    }

    /**
     * 更新水流配置信息
     * @description 单个对象修改
     * @param updateReqVO WaterFlowConfigInfoUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody WaterFlowConfigInfoUpdateReqVO updateReqVO) {
        waterFlowConfigInfoService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新水流配置信息
     * @description 批量更新
     * @param lists 批量更新列表 WaterFlowConfigInfoUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<WaterFlowConfigInfoUpdateReqVO> lists) {
        return success(waterFlowConfigInfoService.updateBatch(lists));
    }

    /**
     * 删除水流配置信息
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        waterFlowConfigInfoService.delete(id);
        return success(true);
    }

    /**
     * 批量删除水流配置信息
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(waterFlowConfigInfoService.deleteBatch(ids));
    }

    /**
     * 获得水流配置信息详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<WaterFlowConfigInfoDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<WaterFlowConfigInfoDetailRespVO> get(@RequestParam("id") Long id) {
        WaterFlowConfigInfoDO waterFlowConfigInfo = waterFlowConfigInfoService.get(id);
        return success(WaterFlowConfigInfoConvert.INSTANCE.convertDetail(waterFlowConfigInfo));
    }

    /**
     * 水流配置信息列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 WaterFlowConfigInfoQueryReqVO
     * @return CommonResult<List<WaterFlowConfigInfoRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<WaterFlowConfigInfoRespVO>> getList(@RequestQueryParam WaterFlowConfigInfoQueryReqVO queryReqVO) {
        List<WaterFlowConfigInfoDO> list = waterFlowConfigInfoService.getList(queryReqVO);
        return success(WaterFlowConfigInfoConvert.INSTANCE.convertList(list));
    }

    /**
     * 水流配置信息分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 WaterFlowConfigInfoPageReqVO
     * @return CommonResult<PageResult<WaterFlowConfigInfoRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<WaterFlowConfigInfoRespVO>> page(@RequestQueryParam WaterFlowConfigInfoPageReqVO pageVO) {
        PageResult<WaterFlowConfigInfoDO> pageResult = waterFlowConfigInfoService.page(pageVO);
        return success(WaterFlowConfigInfoConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出水流配置信息Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 WaterFlowConfigInfoExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam WaterFlowConfigInfoExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<WaterFlowConfigInfoDO> list = waterFlowConfigInfoService.getList(queryReqVO);
        // 导出 Excel
        List<WaterFlowConfigInfoExcelVO> datas = WaterFlowConfigInfoConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "水流配置信息", "xlsx"), queryReqVO.getExportSheetName(),
                                                    WaterFlowConfigInfoExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入水流配置信息模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "水流配置信息-导入模版.xls", "sheet1", WaterFlowConfigInfoExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入水流配置信息Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:water-flow-config-info:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<WaterFlowConfigInfoExcelVO> list = ExcelUtils.read(file, WaterFlowConfigInfoExcelVO.class);
        return success(waterFlowConfigInfoService.importExcel(list, isUpdate));
    }
}