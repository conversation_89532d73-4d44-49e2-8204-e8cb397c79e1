package com.yutoudev.irrigation.irr.controller.large.allocationWater.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 配水计划水渠配水信息ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-配水计划ResponseVO
 * @time 2025-07-07
 */
@Data
@ToString(callSuper = true)
public class WaterPlanChanInfoRespVO {
    /**
     * 渠道ID
     */
    private Long chanId;
    /**
     * 渠道名称
     */
    private String chanName;
    /**
     * 配水
     */
    private Double water;
}
