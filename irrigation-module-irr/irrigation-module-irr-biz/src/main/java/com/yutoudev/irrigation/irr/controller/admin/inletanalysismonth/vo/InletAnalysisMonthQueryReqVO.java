package com.yutoudev.irrigation.irr.controller.admin.inletanalysismonth.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysismonth.InletAnalysisMonthDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;

import java.time.LocalDateTime;


/**
 *
 * 月来水分析list查询RequestVO
 * @description 管理后台-月来水分析list查询RequestVO，参数和 InletAnalysisMonthPageReqVO 是一致的
 * <AUTHOR>
 * @time 2024-07-23 14:33:38
 *
 */
@Data
public class InletAnalysisMonthQueryReqVO extends QueryCriteria<InletAnalysisMonthDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 月
     * 
     */
    private String month;

    /**
     * 累计降雨量
     * 
     */
    private Double rainfall;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     * 
     */
    private LocalDateTime updateTime;

}
