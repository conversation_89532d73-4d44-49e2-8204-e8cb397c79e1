<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.cardbase.CardBaseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectCustomPage" resultType="com.yutoudev.irrigation.irr.controller.admin.cardbase.vo.CardBaseRespVO">
        SELECT
            *
        FROM
        (SELECT
            c.id,
            c.msisdn,
            e.st_name,
            c.iccid,
            c.imsi,
            c.type,
            c.active_date,
            c.open_date,
            c.amount,
            c.over_due,
            c.total_amount,
            c.use_amount,
            c.remain_amount,
            c.status,
            c.creator,
            c.create_time,
            c.update_time,
            c.deleted
        FROM irr_card_base c
            LEFT JOIN irr_equip_base e
            ON c.msisdn = e.msisdn  where c.deleted = 0) as result
            ${ew.customSqlSegment}

    </select>
</mapper>
