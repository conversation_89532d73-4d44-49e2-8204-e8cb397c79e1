package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 *
 * 来水分析明细执行记录中执行数据明细
 * @description 管理后台-来水分析明细执行记录-来水分析明细执行记录中执行数据明细
 * <AUTHOR>
 * @time 2024-08-09 10:49:53
 *
 */
@Data
@ToString(callSuper = true)
public class InletAnalysisDetailExecuteEquipInfoVO {

    /**
     * 设备ID
     * 
     */
    private Long equipId;

    /**
     * 设备状态，状态（0正常 1停用 2报废）-1已删除或数据已不存在
     */
    private Integer equipStatus;

    /**
     * 集水面积（km²）
     */
    private Double area;

    /**
     * 权重值（同集水面积设备占比率），如果有设备状态异常，则权重值重新计算
     */
    private Double ratio;

    /**
     * 监测站雨量值
     */
    private Double rainfall;
}