<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndStatisticsMapper">
    <select id="staticsMonthInflow"
            resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourStaticsMonthInflowRespVO">
        SELECT
        EXTRACT(MONTH FROM hour_time) AS month,
        COALESCE(SUM(reservoir_inflow_volume), 0) AS total_inflow
        FROM
        irr_meas_ind_hour
        WHERE equip_id = #{equipId}
        AND hour_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY
        EXTRACT(MONTH FROM hour_time)
        ORDER BY
        month;
    </select>
    <select id="staticsMonthReservoirCapacity"
            resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourStaticsMonthReservoirCapacityRespVO">
        SELECT
            equip_id,
            EXTRACT(YEAR FROM hour_time) AS year,
            EXTRACT(MONTH FROM hour_time) AS month,
            reservoir_capacity
        FROM
            irr_meas_ind_hour
        WHERE equip_id = #{equipId}
            AND EXTRACT(DAY FROM hour_time) = 1
            AND EXTRACT(HOUR FROM hour_time) = 8
            AND EXTRACT(MINUTE FROM hour_time) = 0
            AND EXTRACT(SECOND FROM hour_time) = 0
            AND hour_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY
            equip_id, year, month;
    </select>
    <select id="staticsRainFallListByCatchmentAreaId"
            resultType="com.yutoudev.irrigation.irr.controller.app.equip.vo.RainfallSiteRespVO">
        SELECT
            equip.id AS id,
            equip.st_name AS st_name,
            equip.central_id AS central_id,
            equip.dev_id AS dev_id,
            equip.pic_url AS pic_url,
            equip.`online` AS `online`,
            equip.take_photo AS take_photo,
            equip.protocol AS protocol,
            measFile.url AS take_photo_url,
            realTime.rainfall AS rainfall,
            realTime.day_rainfall AS day_rainfall,
            realTime.month_rainfall AS month_rainfall,
            realTime.v14 AS voltage,
            indHour.h3Value AS h3value,
            indHour.h6Value AS h6Value,
            indHour.h12Value AS h12Value,
            indHour.h24Value AS h24Value,
            warnInfo.id AS warn_id,
            warnInfo.warn_content AS warn_content,
            warnLevel.warn_color AS warn_color,
            warnLevel.warn_level AS warn_level,
            warnLevel.warn_level_name AS warn_level_name,
            warnInfo.create_time AS warn_time
        FROM
            irr_catchment_area_equip AS cae
        LEFT JOIN irr_equip_base AS equip ON cae.equip_id = equip_id AND cae.central_id = equip.central_id AND cae.dev_id = equip.dev_id
        LEFT JOIN (
            SELECT
                equip_id,
                central_id,
                dev_id,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 3 HOUR THEN rainfall_volume ELSE 0 END ) AS h3Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 6 HOUR THEN rainfall_volume ELSE 0 END ) AS h6Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 12 HOUR THEN rainfall_volume ELSE 0 END ) AS h12Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 24 HOUR THEN rainfall_volume ELSE 0 END ) AS h24Value
            FROM
                irr_meas_ind_hour
            WHERE
                equip_id IN ( SELECT equip_id FROM irr_catchment_area_equip WHERE catchment_id = #{areaId} ) AND hour_time >= NOW() - INTERVAL 24 HOUR
            GROUP BY
                equip_id,
                central_id,
                dev_id
            ) AS indHour ON indHour.equip_id = equip.id AND indHour.central_id = equip.central_id AND indHour.dev_id = equip.dev_id
        LEFT JOIN irr_meas_realtime AS realTime ON realTime.equip_id = equip.id AND realTime.central_id = equip.central_id AND realTime.dev_id = equip.dev_id
        LEFT JOIN (
            SELECT
                id,
                create_time,
                equip_id,
                warn_level_id,
                warn_content
            FROM
                ( SELECT
                    t.*,
                    ROW_NUMBER() OVER ( PARTITION BY t.equip_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
                FROM
                    ew_warn_info t
                WHERE
                    t.deleted = 0 AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
                ) AS sub
            WHERE
                rn = 1
            ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
        LEFT JOIN ( SELECT
                id,
                create_time,
                dev_id,
                central_id,
                url
            FROM
                ( SELECT
                        t.*,
                        ROW_NUMBER() OVER ( PARTITION BY t.central_id,t.dev_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
                    FROM
                        irr_meas_file t
                    WHERE
                        t.deleted = 0
                ) AS sub
            WHERE
                rn = 1) AS measFile ON measFile.central_id = equip.central_id AND measFile.dev_id = equip.dev_id
        WHERE
            cae.catchment_id = #{areaId}
    </select>
    <select id="staticsRainFallListByCatchmentArea"
            resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.RainfallCatchmentSiteRespVO">
        SELECT
            equip.id AS id,
            equip.st_name AS st_name,
            equip.central_id AS central_id,
            equip.dev_id AS dev_id,
            equip.`online` AS `online`,
            ca.id AS catchment_id,
            ca.`name` AS catchment_name,
            cae.area AS catchment_area,
            ca.swhs_id AS swhs_id,
            ca.chan_id AS chan_id,
            realTime.rainfall AS rainfall,
            realTime.day_rainfall AS day_rainfall,
            realTime.month_rainfall AS month_rainfall,
            realTime.v14 AS voltage,
            realTime.update_time AS measure_time,
            indHour.h1Value AS h1value,
            indHour.h3Value AS h3value,
            indHour.h6Value AS h6Value,
            indHour.h12Value AS h12Value,
            indHour.h24Value AS h24Value,
            indHour.h48Value AS h48Value,
            warnInfo.id AS warn_id,
            warnInfo.warn_content AS warn_content,
            warnInfo.warn_color AS warn_color,
            warnLevel.warn_level AS warn_level,
            warnLevel.warn_level_name AS warn_level_name,
            warnInfo.create_time AS warn_time
        FROM
            irr_catchment_area AS ca
        LEFT JOIN irr_catchment_area_equip AS cae ON ca.id = cae.catchment_id
        LEFT JOIN irr_equip_base AS equip ON cae.equip_id = equip_id AND cae.central_id = equip.central_id AND cae.dev_id = equip.dev_id
        LEFT JOIN (
            SELECT
                equip_id,
                central_id,
                dev_id,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 1 HOUR THEN rainfall_volume ELSE 0 END ) AS h1Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 3 HOUR THEN rainfall_volume ELSE 0 END ) AS h3Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 6 HOUR THEN rainfall_volume ELSE 0 END ) AS h6Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 12 HOUR THEN rainfall_volume ELSE 0 END ) AS h12Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 24 HOUR THEN rainfall_volume ELSE 0 END ) AS h24Value,
                SUM( CASE WHEN hour_time >= NOW() - INTERVAL 48 HOUR THEN rainfall_volume ELSE 0 END ) AS h48Value
            FROM
                irr_meas_ind_hour
            WHERE
                equip_id IN ( SELECT equip_id FROM irr_catchment_area_equip ) AND hour_time >= NOW() - INTERVAL 48 HOUR
            GROUP BY
                equip_id,
                central_id,
                dev_id
            ) AS indHour ON indHour.equip_id = equip.id AND indHour.central_id = equip.central_id AND indHour.dev_id = equip.dev_id
        LEFT JOIN irr_meas_realtime AS realTime ON realTime.equip_id = equip.id AND realTime.central_id = equip.central_id AND realTime.dev_id = equip.dev_id
        LEFT JOIN (
            SELECT
                id,
                create_time,
                equip_id,
                warn_level_id,
                warn_color,
                warn_content
            FROM
                ( SELECT
                    t.*,
                    ROW_NUMBER() OVER ( PARTITION BY t.equip_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
                FROM
                    ew_warn_info t
                WHERE
                    t.deleted = 0 AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
                ) AS sub
            WHERE
                rn = 1
            ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id

    </select>
    <select id="listMeasureSiteByDevType"
            resultType="com.yutoudev.irrigation.irr.controller.app.equip.vo.MeasureSiteRespVO">
		SELECT
		  equip.id AS id,
		  measTime.water_level AS waterLevel,
		  measTime.water_speed AS water_speed,
		  measTime.calc_water_flow AS calc_water_flow,
		  measTime.calc_water_volume AS calc_water_volume,
		  measTime.voltage AS voltage,
		  measTime.report_time AS report_time,
		  chan.id AS chan_id,
		  chan.chan_name AS chan_name,
		  chan.parent_id AS parent_id,
		  chan.parent_tree_id AS parent_tree_id,
		  chan.swhs_id AS swhs_id,
		  chan.max_warning_value AS max_warning_value,
		  chan.chan_type AS chan_type,
		  chan.mlg_num AS chan_mlg_num,
		  chan.sort AS chan_sort,
		  equip.st_name AS st_name,
		  equip.dev_id AS dev_id,
          equip.central_id AS central_id,
          equip.dev_type AS dev_type,
		  equip.pic_url AS pic_url,
		  equip.`online` AS `online`,
		  equip.take_photo AS take_photo,
		  equip.protocol AS protocol,
		  equip.sort AS equip_sort,
		  equip.dpds_num AS equip_mlg_num,
          measFile.url AS take_photo_url,
		  warnInfo.id AS warn_id,
		  warnInfo.warn_content AS warn_content,
          warnLevel.warn_color AS warn_color,
		  warnLevel.warn_level AS warn_level,
		  warnLevel.warn_level_name AS warn_level_name,
		  warnInfo.create_time AS warn_time 
		FROM
		  irr_equip_base AS equip
          LEFT JOIN (
              SELECT
                *
              FROM
                ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY equip_id ORDER BY report_time DESC ) AS rn FROM irr_meas_ind_time WHERE  report_time >= NOW() - INTERVAL 10 MINUTE ) t
              WHERE
                rn = 1
              ) AS measTime ON equip.id = measTime.equip_id
		  LEFT JOIN ( SELECT equip_id, chan_id FROM irr_chan_equip_query ) AS chanEquip ON chanEquip.equip_id = equip.id
		  LEFT JOIN irr_chan_base AS chan ON chan.id = chanEquip.chan_id
		  LEFT JOIN (
              SELECT
                t.id,
                t.create_time,
                t.equip_id,
                t.warn_level_id,
                t.warn_content
              FROM
                ew_warn_info t
                INNER JOIN (
                    SELECT
                      equip_id,
                      MAX( create_time ) AS max_time
                    FROM
                      ew_warn_info
                    WHERE
                      equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1)
                      and deleted = 0
                      AND create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
                    GROUP BY
                      equip_id
                ) m ON t.equip_id = m.equip_id AND t.create_time = m.max_time
              ) AS warnInfo ON warnInfo.equip_id = equip.id
		  LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
          LEFT JOIN ( SELECT
                    id,
                    create_time,
                    dev_id,
                    central_id,
                    url
                FROM
                    ( SELECT
                        t.*,
                        ROW_NUMBER() OVER ( PARTITION BY t.central_id,t.dev_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
                    FROM
                        irr_meas_file t
                    WHERE
                        t.deleted = 0
                    ) AS sub
                WHERE
                    rn = 1) AS measFile ON measFile.central_id = equip.central_id AND measFile.dev_id = equip.dev_id
		WHERE
		  equip.deleted = 0  AND equip.st_type = 1 AND equip.dev_type in
        <foreach collection="devTypes" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="reservoir">
            AND chan.parent_tree_id is null and chan.swhs_id is not null and chan.chan_type>2
        </if>
    </select>

    <select id="listMeasureReservoirSite"
            resultType="com.yutoudev.irrigation.irr.controller.app.equip.vo.MeasureReservoirSiteRespVO">
		SELECT 
            swhs.id as swhs_id, swhs.swhs_name as swhs_name,
            equip.id AS equip_id,
            equip.st_name AS st_name,
            equip.dev_id AS dev_id,
            equip.central_id AS central_id,
            equip.dev_type AS dev_type,
            equip.pic_url AS pic_url,
            equip.`online` AS `online`,
            equip.take_photo AS take_photo,
            equip.protocol AS protocol,
            equip.sort AS equip_sort,
            equip.dpds_num AS equip_mlg_num,
            measFile.url AS take_photo_url,
            indHour.update_time AS update_time,
            indHour.rainfall_volume AS rainfall_volume,
            indHour.water_level AS water_level,
            indHour.reservoir_capacity AS reservoir_capacity,
            indHour.reservoir_inflow AS reservoir_inflow,
            indHour.reservoir_outflow AS reservoir_outflow,
            indHour.voltage AS voltage,
            warnInfo.id AS warn_id,
            warnInfo.warn_content AS warn_content,
            warnLevel.warn_color AS warn_color,
            warnLevel.warn_level AS warn_level,
            warnLevel.warn_level_name AS warn_level_name,
            warnInfo.create_time AS warn_time
        FROM irr_swhs_base as swhs
        LEFT JOIN (SELECT * from irr_equip_base where deleted=0 and st_type=1 and dev_type=1102)  as equip on equip.swhs_id = swhs.id
        LEFT JOIN (  SELECT  *  FROM
        ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY equip_id ORDER BY hour_time DESC ) AS rn FROM irr_meas_ind_hour WHERE equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1 and dev_type=1102) and hour_time >= NOW() - INTERVAL  1 HOUR  )  as t
        WHERE
        rn = 1 ) as indHour on indHour.equip_id = equip.id
        LEFT JOIN (
          SELECT
            id,
            create_time,
            equip_id,
            warn_level_id,
            warn_content
          FROM
            ( SELECT
              t.*,
              ROW_NUMBER() OVER ( PARTITION BY t.equip_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
            FROM
              ew_warn_info t
            WHERE
              t.equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1 and dev_type=1102)
              and t.deleted = 0
              AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
            ) AS sub
          WHERE
            rn = 1
          ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
        LEFT JOIN ( SELECT
                id,
                create_time,
                dev_id,
                central_id,
                url
            FROM
                ( SELECT
                    t.*,
                    ROW_NUMBER() OVER ( PARTITION BY t.central_id,t.dev_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
                FROM
                    irr_meas_file t
                WHERE
                    t.deleted = 0
                ) AS sub
            WHERE
            rn = 1) AS measFile ON measFile.central_id = equip.central_id AND measFile.dev_id = equip.dev_id
		WHERE swhs.deleted=0 

    </select>
</mapper>
