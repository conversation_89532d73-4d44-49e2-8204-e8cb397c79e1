package com.yutoudev.irrigation.irr.controller.large.plan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 抗旱预案ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class DroughtPlanLargeRespVO {


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 预案名称
     */
    private String planName;

    /**
     * 预案编号
     */
    private String planCode;

    /**
     * 预案等级
     */
    private Integer planLevel;

    /**
     * 预案等级标签
     */
    private String planLevelLabel;

    /**
     * 预案类型
     */
    private Integer planType;

    /**
     * 预案状态
     *
     * @mock 0=失效,1=有效,2=待修订
     */
    private Integer planStatus;

    /**
     * 预案状态标签
     */
    private String planStatusLabel;

    /**
     * 预案内容描述
     */
    private String planDesc;

    /**
     * 制定日期
     */
    private LocalDate planDate;

    /**
     * 修订日期
     */
    private LocalDate reviseDate;

    /**
     * 附件
     */
    private String files;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 删除标志
     */
    private Boolean deleted;

    /**
     * 租户编号
     */
    private Long tenantId;
}