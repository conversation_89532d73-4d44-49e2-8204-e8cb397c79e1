package com.yutoudev.irrigation.irr.controller.large.oa;

import com.yutoudev.irrigation.archive.api.ArchiveLargeApi;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.oa.api.OALargeApi;
import com.yutoudev.irrigation.oa.api.dto.OALargeDTO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 办公OA大屏接口
 */
@RestController
@RequestMapping("/irr/large-oa")
@Validated
public class OALargeController {

    @Resource
    private OALargeApi oaLargeApi;

    @Resource
    private ArchiveLargeApi archiveLargeApi;

    @GetMapping("/total")
    public CommonResult<OALargeDTO> getTotal(){
        OALargeDTO total = oaLargeApi.getTotal();
        total.setInstitutionTotal(archiveLargeApi.getInstitutionTotal());
        return CommonResult.success(total);
    }
}
