package com.yutoudev.irrigation.irr.controller.admin.allocationplanmonth.vo;

import lombok.*;
import java.util.*;
import java.time.*;
    import java.math.BigDecimal;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationplanmonth.AllocationPlanMonthDO;
import org.springframework.format.annotation.DateTimeFormat;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;

/**
 *
 * 配水调度月计划分页RequestVO
 * @description 管理后台-配水调度月计划分页RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:39
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AllocationPlanMonthPageReqVO extends PageCriteria<AllocationPlanMonthDO> {

    /**
     * 计划名称
     * 
     */
    private String name;

    /**
     * 用水用户ID
     * 
     */
    private Long userId;

    /**
     * 灌溉片区ID
     * 
     */
    private Long zoneId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 计划供水
     * @mock （m³）
     */
    private BigDecimal supplyWater;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 计划时间
     * @mock （yyyy-MM）
     */
    private String planTime;

    /**
     * 创建时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}
