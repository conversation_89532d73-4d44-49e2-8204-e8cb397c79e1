package com.yutoudev.irrigation.irr.service.history;

import com.yutoudev.irrigation.framework.common.util.object.ReflectionUtils;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipcalcconfig.EquipCalcConfigDO;
import com.yutoudev.irrigation.irr.dal.dataobject.history.*;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.enums.EquipDataCalcFrequencyEnum;
import com.yutoudev.irrigation.irr.enums.HistoryAvgTypeEnum;
import com.yutoudev.irrigation.irr.enums.StatisticalFrequencyEnum;
import com.yutoudev.irrigation.irr.service.equipcalcconfig.EquipCalcConfigService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndHourService;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsBaseService;
import com.yutoudev.irrigation.irr.service.waterdatacalc.ReservoirDataCalcService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;
import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;
import static com.yutoudev.irrigation.irr.enums.HistoryGlobalConstants.HISTORY_TABLE_FIELD_MONTH_ABBR;
import static com.yutoudev.irrigation.irr.enums.IrrigationGlobalConstants.SYSTEM_CREATOR;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HistoryWaterDataCalcServiceImpl implements HistoryWaterDataCalcService {

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Resource
    private HistoryInflowDayService<HistoryInflowDayDO> historyInflowDayService;

    @Resource
    private HistoryInflowService<HistoryInflowDO> historyInflowService;

    @Resource
    private HistoryCapacityDayService<HistoryCapacityDayDO> historyCapacityDayService;

    @Resource
    private HistoryCapacityService<HistoryCapacityDO> historyCapacityService;

    @Resource
    private HistoryAvgDayService<HistoryAvgDayDO> historyAvgDayService;

    @Resource
    private HistoryAvgService<HistoryAvgDO> historyAvgService;

    @Resource
    private SwhsBaseService<SwhsBaseDO> swhsBaseService;

    @Resource
    private EquipCalcConfigService<EquipCalcConfigDO> equipCalcConfigService;

    @Resource
    private ReservoirDataCalcService reservoirDataCalcService;

    /**
     * 获取昨天的日期
     */
    private LocalDate getYesterday() {
        return LocalDate.now().minusDays(1);
    }

    /**
     * 获取昨天的日期时间
     */
    private LocalDateTime getYesterdayDateTime() {
        return LocalDateTime.now().minusDays(1);
    }

    /**
     * 保存或更新历史数据的通用方法
     */
    private <T> void saveOrUpdate(T entity, java.util.function.Supplier<T> querySupplier,
                                  java.util.function.Consumer<T> saveAction,
                                  java.util.function.Consumer<T> updateAction) {
        T existing = querySupplier.get();
        if (Objects.isNull(existing) || (existing instanceof HistoryInflowDO && Objects.isNull(((HistoryInflowDO) existing).getId()))
                || (existing instanceof HistoryCapacityDO && Objects.isNull(((HistoryCapacityDO) existing).getId()))) {
            saveAction.accept(entity);
        } else {
            updateAction.accept(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHistoryWaterData() {
        log.info("开始保存历史水数据");
        try {
            // 暂取主水源
            EquipBaseCacheVO equip = reservoirDataCalcService.getMainWaterSourceEquip();

            Long mainReservoirId = equipCalcConfigService.getCacheMainWaterSourceConfig().getWaterSourceId();
            SwhsBaseDO waterSource = swhsBaseService.getById(mainReservoirId);

            LocalDateTime beginTime = LocalDateTime.now().minusDays(1).withHour(8).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endTime = LocalDateTime.now().withHour(8).withMinute(0).withMinute(0).withNano(0);

            HashMap<String, Double> dataMap = new HashMap<>();

            // 历史来水数据保存
            saveInflowWaterData(dataMap, waterSource, equip, beginTime, endTime);

            // 历史库容数据保存
            saveCapacityWaterData(dataMap, waterSource, equip, beginTime);

            // 历史均值数据保存
            saveAvgData(dataMap, waterSource);

            log.info("历史水数据保存完成");
        } catch (Exception e) {
            log.error("保存历史水数据失败", e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }

    /**
     * 写入来水数据
     *
     * @param dataMap 数据缓存
     * @param waterSource 水源
     * @param equip 水源设备
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    protected void saveInflowWaterData(HashMap<String, Double> dataMap,
                                       SwhsBaseDO waterSource,
                                       EquipBaseCacheVO equip,
                                       LocalDateTime beginTime,
                                       LocalDateTime endTime) {
        log.debug("开始保存来水数据，水源ID: {}", waterSource.getId());

        LocalDate yesterday = getYesterday();
        LocalDateTime yesterdayDateTime = getYesterdayDateTime();

        // 日来水数据
        List<MeasIndHourDO> dayList = measIndHourService.getListByEquipAndDateRange(equip, beginTime, endTime);
        double inflowVolume = dayList.isEmpty() ? 0
                : dayList.stream().mapToDouble(MeasIndHourDO::getReservoirInflowVolume).sum();
        dataMap.put("inflowVolume", inflowVolume);

        // 保存日来水数据
        HistoryInflowDayDO historyInflowDay = createHistoryInflowDay(waterSource, inflowVolume, yesterday);
        historyInflowDayService.save(historyInflowDay);

        // 保存月来水数据
        String monthFormat = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_SHORT));
        String monthName = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_ZH));
        HistoryInflowDO historyMonth = assembleHistoryInflow(inflowVolume, waterSource,
                monthFormat, monthName, StatisticalFrequencyEnum.MONTH.getValue());
        saveOrUpdate(historyMonth,
                () -> historyInflowService.getByWaterSourceAndFrequency(waterSource.getId(),
                        StatisticalFrequencyEnum.MONTH.getValue(), Long.parseLong(monthFormat)),
                historyInflowService::save,
                historyInflowService::updateById);

        // 保存年来水数据
        String yearFormat = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR));
        String yearName = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_ZH));
        HistoryInflowDO historyYear = assembleHistoryInflow(inflowVolume, waterSource,
                yearFormat, yearName, StatisticalFrequencyEnum.YEAR.getValue());
        saveOrUpdate(historyYear,
                () -> historyInflowService.getByWaterSourceAndFrequency(waterSource.getId(),
                        StatisticalFrequencyEnum.YEAR.getValue(), Long.parseLong(yearFormat)),
                historyInflowService::save,
                historyInflowService::updateById);

        log.debug("来水数据保存完成，来水量: {}", inflowVolume);
    }

    /**
     * 创建历史来水日数据
     */
    private HistoryInflowDayDO createHistoryInflowDay(SwhsBaseDO waterSource, double inflowVolume, LocalDate markDay) {
        HistoryInflowDayDO historyInflowDay = new HistoryInflowDayDO();
        historyInflowDay.setMarkDay(markDay);
        historyInflowDay.setSwhsId(waterSource.getId());
        historyInflowDay.setSwhsName(waterSource.getSwhsName());
        historyInflowDay.setInflow(inflowVolume);
        return historyInflowDay;
    }

    private HistoryInflowDO assembleHistoryInflow(double inflowVolume,
                                                  SwhsBaseDO waterSource,
                                                  String format,
                                                  String name,
                                                  int frequency) {
        HistoryInflowDO historyInflow = historyInflowService.getByWaterSourceAndFrequency(waterSource.getId(), frequency,
                Long.parseLong(format));
        LocalDate yesterday = getYesterday();

        if (Objects.isNull(historyInflow)) {
            historyInflow = createNewHistoryInflow(waterSource, inflowVolume, format, name, frequency, yesterday);
        } else {
            updateExistingHistoryInflow(historyInflow, inflowVolume, yesterday);
        }

        historyInflow.setLister(SYSTEM_CREATOR);
        historyInflow.setMarkTime(LocalDateTime.now());
        return historyInflow;
    }

    /**
     * 创建新的历史来水记录
     */
    private HistoryInflowDO createNewHistoryInflow(SwhsBaseDO waterSource, double inflowVolume,
                                                   String format, String name, int frequency, LocalDate yesterday) {
        HistoryInflowDO historyInflow = new HistoryInflowDO();
        historyInflow.setName(name);
        historyInflow.setCode(Long.parseLong(format));
        historyInflow.setFrequency(frequency);
        historyInflow.setSwhsId(waterSource.getId());
        historyInflow.setSwhsName(waterSource.getSwhsName());
        historyInflow.setTotal(inflowVolume);
        historyInflow.setMaxDay(yesterday);
        historyInflow.setMaxVolume(inflowVolume);
        historyInflow.setMinDay(yesterday);
        historyInflow.setMinVolume(inflowVolume);
        historyInflow.setAvgVolume(inflowVolume);
        return historyInflow;
    }

    /**
     * 更新现有的历史来水记录
     */
    private void updateExistingHistoryInflow(HistoryInflowDO historyInflow, double inflowVolume, LocalDate yesterday) {
        historyInflow.setTotal(historyInflow.getTotal() + inflowVolume);
        if (inflowVolume > historyInflow.getMaxVolume()) {
            historyInflow.setMaxVolume(inflowVolume);
            historyInflow.setMaxDay(yesterday);
        }
        if (inflowVolume < historyInflow.getMinVolume()) {
            historyInflow.setMinVolume(inflowVolume);
            historyInflow.setMinDay(yesterday);
        }
        double avg = convert((historyInflow.getAvgVolume() + inflowVolume) / 2, 1);
        historyInflow.setAvgVolume(avg);
    }

    private void saveCapacityWaterData(Map<String, Double> dataMap,
                                       SwhsBaseDO waterSource,
                                       EquipBaseCacheVO equip,
                                       LocalDateTime beginTime) {
        log.debug("开始保存库容数据，水源ID: {}", waterSource.getId());

        LocalDate yesterday = getYesterday();
        LocalDateTime yesterdayDateTime = getYesterdayDateTime();
        LocalDate beginDate = beginTime.toLocalDate();

        // 计算并保存日库容量
        double storageCapacity = reservoirDataCalcService.getReservoirCapacityByHistory(
                equip.getCalcResourceId(), beginDate, EquipDataCalcFrequencyEnum.DAY.getValue());
        dataMap.put("storageCapacity", storageCapacity);
        HistoryCapacityDayDO historyCapacityDay = createHistoryCapacityDay(storageCapacity, waterSource, yesterday);
        historyCapacityDayService.save(historyCapacityDay);

        // 计算并保存月库容量
        String monthFormat = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_SHORT));
        String monthName = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_ZH));
        double monthStorageCapacity = reservoirDataCalcService.getReservoirCapacityByHistory(
                equip.getCalcResourceId(), beginDate, EquipDataCalcFrequencyEnum.MONTH.getValue());
        HistoryCapacityDO historyCapacityMonth = assembleHistoryCapacity(monthStorageCapacity, waterSource,
                monthFormat, monthName, EquipDataCalcFrequencyEnum.MONTH.getValue());
        saveOrUpdate(historyCapacityMonth,
                () -> historyCapacityService.getByWaterSourceAndFrequency(waterSource.getId(),
                        EquipDataCalcFrequencyEnum.MONTH.getValue(), Long.parseLong(monthFormat)),
                historyCapacityService::save,
                historyCapacityService::updateById);

        // 计算并保存年库容量
        String yearFormat = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR));
        String yearName = yesterdayDateTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_ZH));
        double yearStorageCapacity = reservoirDataCalcService.getReservoirCapacityByHistory(
                equip.getCalcResourceId(), beginDate, EquipDataCalcFrequencyEnum.YEAR.getValue());
        HistoryCapacityDO historyCapacityYear = assembleHistoryCapacity(yearStorageCapacity, waterSource,
                yearFormat, yearName, EquipDataCalcFrequencyEnum.YEAR.getValue());
        saveOrUpdate(historyCapacityYear,
                () -> historyCapacityService.getByWaterSourceAndFrequency(waterSource.getId(),
                        EquipDataCalcFrequencyEnum.YEAR.getValue(), Long.parseLong(yearFormat)),
                historyCapacityService::save,
                historyCapacityService::updateById);

        log.debug("库容数据保存完成，日库容: {}, 月库容: {}, 年库容: {}",
                storageCapacity, monthStorageCapacity, yearStorageCapacity);
    }

    /**
     * 创建历史库容日数据
     */
    private HistoryCapacityDayDO createHistoryCapacityDay(double storageCapacity, SwhsBaseDO waterSource, LocalDate markDay) {
        HistoryCapacityDayDO historyCapacityDay = new HistoryCapacityDayDO();
        historyCapacityDay.setMarkDay(markDay);
        historyCapacityDay.setSwhsId(waterSource.getId());
        historyCapacityDay.setSwhsName(waterSource.getSwhsName());
        historyCapacityDay.setCapacity(storageCapacity);
        return historyCapacityDay;
    }

    private HistoryCapacityDO assembleHistoryCapacity(double storageCapacity, SwhsBaseDO waterSource, String format,
                                                      String name, int frequency) {
        HistoryCapacityDO historyCapacity = historyCapacityService.getByWaterSourceAndFrequency(waterSource.getId(), frequency,
                Long.parseLong(format));
        LocalDate yesterday = getYesterday();

        if (Objects.isNull(historyCapacity)) {
            historyCapacity = createNewHistoryCapacity(waterSource, storageCapacity, format, name, frequency, yesterday);
        } else {
            updateExistingHistoryCapacity(historyCapacity, storageCapacity, yesterday);
        }

        historyCapacity.setLister(SYSTEM_CREATOR);
        historyCapacity.setMarkTime(LocalDateTime.now());
        return historyCapacity;
    }

    /**
     * 创建新的历史库容记录
     */
    private HistoryCapacityDO createNewHistoryCapacity(SwhsBaseDO waterSource, double storageCapacity,
                                                       String format, String name, int frequency, LocalDate yesterday) {
        HistoryCapacityDO historyCapacity = new HistoryCapacityDO();
        historyCapacity.setName(name);
        historyCapacity.setCode(Long.parseLong(format));
        historyCapacity.setFrequency(frequency);
        historyCapacity.setSwhsId(waterSource.getId());
        historyCapacity.setSwhsName(waterSource.getSwhsName());
        historyCapacity.setTotal(storageCapacity);
        historyCapacity.setMaxDay(yesterday);
        historyCapacity.setMaxVolume(storageCapacity);
        historyCapacity.setMinDay(yesterday);
        historyCapacity.setMinVolume(storageCapacity);
        historyCapacity.setAvgVolume(storageCapacity);
        return historyCapacity;
    }

    /**
     * 更新现有的历史库容记录
     */
    private void updateExistingHistoryCapacity(HistoryCapacityDO historyCapacity, double storageCapacity, LocalDate yesterday) {
        historyCapacity.setTotal(storageCapacity);
        if (storageCapacity > historyCapacity.getMaxVolume()) {
            historyCapacity.setMaxVolume(storageCapacity);
            historyCapacity.setMaxDay(yesterday);
        }
        if (storageCapacity < historyCapacity.getMinVolume()) {
            historyCapacity.setMinVolume(storageCapacity);
            historyCapacity.setMinDay(yesterday);
        }
        double avg = convert((historyCapacity.getAvgVolume() + storageCapacity) / 2, 1);
        historyCapacity.setAvgVolume(avg);
    }

    private void saveAvgData(HashMap<String, Double> dataMap, SwhsBaseDO waterSource) {
        log.debug("开始保存均值数据，水源ID: {}", waterSource.getId());

        double inflowVolume = dataMap.get("inflowVolume");
        double storageCapacity = dataMap.get("storageCapacity");

        // 计算日均值
        List<HistoryAvgDayDO> historyAvgDayList = assembleHistoryAvgDay(waterSource, inflowVolume, storageCapacity);
        if (!historyAvgDayList.isEmpty()) {
            historyAvgDayService.updateBatchById(historyAvgDayList);
        }

        // 计算月均值
        List<HistoryAvgDO> historyAvgMonthList = assembleHistoryAvgMonth(waterSource, inflowVolume, storageCapacity);
        if (!historyAvgMonthList.isEmpty()) {
            historyAvgService.updateBatchById(historyAvgMonthList);
        }

        log.debug("均值数据保存完成");
    }

    private List<HistoryAvgDayDO> assembleHistoryAvgDay(SwhsBaseDO waterSource, double inflowVolume, double storageCapacity) {
        List<HistoryAvgDayDO> list = new ArrayList<>();
        LocalDate yesterday = getYesterday();
        String dayFormat = yesterday.format(DateTimeFormatter.ofPattern(FORMAT_MONTH_DAY_SHORT));
        int dayFormatInt = Integer.parseInt(dayFormat);

        // 处理来水均值
        HistoryAvgDayDO historyInflowVolumeAvgDay = historyAvgDayService.getByWaterSourceAndTypeAndDay(
                waterSource.getId(), HistoryAvgTypeEnum.INFLOW.getType(), dayFormatInt);
        if (Objects.nonNull(historyInflowVolumeAvgDay)) {
            double inflowVolumeAvg = convert((historyInflowVolumeAvgDay.getAvgValue() + inflowVolume) / 2, 1);
            historyInflowVolumeAvgDay.setAvgValue(inflowVolumeAvg);
            list.add(historyInflowVolumeAvgDay);
        }

        // 处理库容均值
        HistoryAvgDayDO historyStorageCapacityAvgDay = historyAvgDayService.getByWaterSourceAndTypeAndDay(
                waterSource.getId(), HistoryAvgTypeEnum.CAPACITY.getType(), dayFormatInt);
        if (Objects.nonNull(historyStorageCapacityAvgDay)) {
            double storageCapacityAvg = convert((historyStorageCapacityAvgDay.getAvgValue() + storageCapacity) / 2, 1);
            historyStorageCapacityAvgDay.setAvgValue(storageCapacityAvg);
            list.add(historyStorageCapacityAvgDay);
        }

        return list;
    }

    private List<HistoryAvgDO> assembleHistoryAvgMonth(SwhsBaseDO waterSource, double inflowVolume, double storageCapacity) {
        List<HistoryAvgDO> list = new ArrayList<>();
        LocalDate yesterday = getYesterday();
        int month = yesterday.getMonthValue();

        // 处理来水月均值
        HistoryAvgDO historyInflowVolumeAvgMonth = historyAvgService.getByWaterSourceAndType(
                waterSource.getId(), HistoryAvgTypeEnum.INFLOW.getType());
        if (Objects.nonNull(historyInflowVolumeAvgMonth)) {
            updateMonthlyAvg(historyInflowVolumeAvgMonth, month, inflowVolume);
            list.add(historyInflowVolumeAvgMonth);
        }

        // 处理库容月均值
        HistoryAvgDO historyStorageCapacityAvgMonth = historyAvgService.getByWaterSourceAndType(
                waterSource.getId(), HistoryAvgTypeEnum.CAPACITY.getType());
        if (Objects.nonNull(historyStorageCapacityAvgMonth)) {
            updateMonthlyAvg(historyStorageCapacityAvgMonth, month, storageCapacity);
            list.add(historyStorageCapacityAvgMonth);
        }

        return list;
    }

    /**
     * 更新月度均值数据
     */
    private void updateMonthlyAvg(HistoryAvgDO historyAvg, int month, double newValue) {
        String monthField = HISTORY_TABLE_FIELD_MONTH_ABBR + month;
        double currentMonthValue = (double) ReflectionUtils.getFieldValue(historyAvg, monthField);
        double monthTotal = historyAvg.getTotal() - currentMonthValue;
        double newMonthAvg = convert((currentMonthValue + newValue) / 2, 1);

        ReflectionUtils.setFieldValue(historyAvg, monthField, newMonthAvg);
        historyAvg.setTotal(monthTotal + newMonthAvg);
    }
}
