package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 来水分析明细更新RequestVO
 * @description 管理后台-来水分析明细更新RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:37
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisDetailUpdateReqVO extends InletAnalysisDetailBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}