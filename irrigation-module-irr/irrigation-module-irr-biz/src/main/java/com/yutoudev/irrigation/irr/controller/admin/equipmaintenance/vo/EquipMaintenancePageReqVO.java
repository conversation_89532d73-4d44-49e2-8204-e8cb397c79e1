package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.equipmaintenance.EquipMaintenanceDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 设备维护分页RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护分页RequestVO
 * @time 2024-10-27 10:33:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EquipMaintenancePageReqVO extends PageCriteria<EquipMaintenanceDO> {

    /**
     * 设备ID
     */
    private Long equipId;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 维护内容
     */
    private String description;

    /**
     * 维护结果
     */
    private String result;

    /**
     * 维护人员ID
     */
    private Long userId;

    /**
     * 维护日期
     */
    private LocalDateTime maintenanceDate;

}
