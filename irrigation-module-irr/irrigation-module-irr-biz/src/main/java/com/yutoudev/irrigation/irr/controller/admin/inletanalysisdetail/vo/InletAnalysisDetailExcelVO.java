package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 *
 * 来水分析明细ExcelVO
 * @description 管理后台-来水分析明细导出、导入ExcelVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:37
 *
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class InletAnalysisDetailExcelVO {


    /**
     * 集水面积名称
     */
    @ExcelProperty("集水面积")
    private String catchmentName;

    /**
     * 水源名称
     */
    @ExcelProperty("水源")
    private String swhsName;

    /**
     * 水渠名称
     */
    @ExcelProperty("水渠")
    private String chanName;


    /**
     * 降雨量
     */
    @ExcelProperty("降雨量（m³）")
    private Double rainfall;

    /**
     * 蒸发量
     * 
     */
    @ExcelProperty("蒸发量（mm/m²）")
    private Double evaporation;

    /**
     * 损耗值
     * 
     */
    @ExcelProperty("损耗值（比率）")
    private Double loss;

    /**
     * 径流值
     * 
     */
    @ExcelProperty("径流值（比率）")
    private Double runoff;

    /**
     * 径流时间
     * @mock （分钟）
     */
    @ExcelProperty("径流时间（分）")
    private Integer runoffTime;


    /**
     * 来水时间
     */
    @ExcelProperty("来水时间")
    private LocalDateTime realtime;

}
