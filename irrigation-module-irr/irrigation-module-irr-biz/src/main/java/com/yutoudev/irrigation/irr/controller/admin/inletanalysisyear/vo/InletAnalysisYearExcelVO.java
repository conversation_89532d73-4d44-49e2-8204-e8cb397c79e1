package com.yutoudev.irrigation.irr.controller.admin.inletanalysisyear.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 年来水分析ExcelVO
 *
 * <AUTHOR>
 * @description 管理后台-年来水分析导出、导入ExcelVO
 * @time 2024-07-23 14:33:39
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class InletAnalysisYearExcelVO {

    /**
     * 集水面积名称
     */
    @ExcelProperty("集水面积")
    private String catchmentName;

    /**
     * 水源名称
     */
    @ExcelProperty("水源")
    private String swhsName;

    /**
     * 水渠名称
     */
    @ExcelProperty("水渠")
    private String chanName;

    /**
     * 年
     */
    @ExcelProperty("年")
    private String year;

    /**
     * 累计降雨量
     */
    @ExcelProperty("累计降雨量（m³）")
    private Double rainfall;


    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;
}
