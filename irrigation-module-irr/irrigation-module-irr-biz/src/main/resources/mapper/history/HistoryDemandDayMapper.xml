<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.history.HistoryDemandDayMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <insert id="autoSaveByTime">
        INSERT INTO irr_history_demand_day (mark_day, swhs_id, swhs_name, demand_water)
        SELECT
            STR_TO_DATE(DATE_FORMAT(start_time, '%Y-%m-%d'), '%Y-%m-%d') AS mark_day,
            swhs_id,
            MAX(swhs_name) AS swhs_name,  -- 使用MAX确保名称唯一(相同ID应有相同名称)
            SUM(water) AS demand_water
        FROM
            irr_allocation_water
        WHERE
            DATE_FORMAT(start_time, #{timeFormat} ) = #{time}
        GROUP BY
            swhs_id,
            DATE_FORMAT(start_time, '%Y-%m-%d')
            ON DUPLICATE KEY UPDATE
                                 demand_water = VALUES(demand_water),
                                 swhs_name = VALUES(swhs_name)
    </insert>
</mapper>
