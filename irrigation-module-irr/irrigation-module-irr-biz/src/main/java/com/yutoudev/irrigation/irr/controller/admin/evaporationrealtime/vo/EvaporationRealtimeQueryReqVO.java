package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.evaporationrealtime.EvaporationRealtimeDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 实时蒸散发量list查询RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-实时蒸散发量list查询RequestVO，参数和 EvaporationRealtimePageReqVO 是一致的
 * @time 2024-12-20 17:34:41
 */
@Data
public class EvaporationRealtimeQueryReqVO extends QueryCriteria<EvaporationRealtimeDO> {

    /**
     * ID
     */
    private Long id;

    /**
     * 时间
     */
    private LocalDateTime markTime;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 水面蒸发值
     */
    private Double evaporationValue;

}
