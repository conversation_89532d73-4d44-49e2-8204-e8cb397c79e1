package com.yutoudev.irrigation.irr.controller.large.flum;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.flumbase.vo.FlumBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.flumbase.vo.FlumBaseRespVO;
import com.yutoudev.irrigation.irr.controller.large.flum.vo.BuildingStatsRespVO;
import com.yutoudev.irrigation.irr.convert.flumbase.FlumBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.flumbase.FlumBaseDO;
import com.yutoudev.irrigation.irr.enums.BuildingEngStatsEnum;
import com.yutoudev.irrigation.irr.service.flumbase.FlumBaseService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 大屏 - 渡槽统计
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/flum-base")
@Validated
@Slf4j
public class LargeFlumBaseController {

    private static final String MODULE_NAME = "大屏-渡槽统计";

    @Resource
    private FlumBaseService<FlumBaseDO> flumBaseService;

    /**
     * 获取渡槽工程建设统计
     */
    @GetMapping("/building-stats")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<BuildingStatsRespVO> statisticsBuilding() {
        return success(flumBaseService.statisticsBuilding());
    }

    /**
     * 重建渡槽分页
     *
     * @param pageVO 查询条件 FlumBasePageReqVO
     */
    @GetMapping("/page-rebuilding")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<FlumBaseRespVO>> pageRebuilding(@RequestQueryParam FlumBasePageReqVO pageVO) {
        pageVO.setEngStat(BuildingEngStatsEnum.REBUILDING.getStatus());
        PageResult<FlumBaseDO> pageResult = flumBaseService.page(pageVO);
        return success(FlumBaseConvert.INSTANCE.convertPage(pageResult));
    }
}