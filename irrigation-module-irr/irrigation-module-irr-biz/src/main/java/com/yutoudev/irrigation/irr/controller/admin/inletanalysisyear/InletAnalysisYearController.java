package com.yutoudev.irrigation.irr.controller.admin.inletanalysisyear;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.constraints.*;
import javax.validation.*;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;


import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yutoudev.irrigation.irr.controller.admin.inletanalysisyear.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisyear.InletAnalysisYearDO;
import com.yutoudev.irrigation.irr.convert.inletanalysisyear.InletAnalysisYearConvert;
import com.yutoudev.irrigation.irr.service.inletanalysisyear.InletAnalysisYearService;


/**
 *
 * 年来水分析
 * @description 管理后台-年来水分析controller
 * <AUTHOR>
 * @time 2024-07-23 14:33:39
 *
 */
@RestController
@RequestMapping("/irr/inlet-analysis-year")
@Validated
public class InletAnalysisYearController {

    private static final String MODULE_NAME = "年来水分析";

    @Resource
    private InletAnalysisYearService<InletAnalysisYearDO> inletAnalysisYearService;

    /**
     * 创建年来水分析
     * @description 单个对象保存
     * @param createReqVO InletAnalysisYearCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody InletAnalysisYearCreateReqVO createReqVO) {
        return success(inletAnalysisYearService.create(createReqVO));
    }

    /**
     * 批量创建年来水分析
     * @description 多个对象保存
     * @param lists  InletAnalysisYearCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<InletAnalysisYearCreateReqVO> lists) {
        return success(inletAnalysisYearService.createBatch(lists));
    }

    /**
     * 更新年来水分析
     * @description 单个对象修改
     * @param updateReqVO InletAnalysisYearUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody InletAnalysisYearUpdateReqVO updateReqVO) {
        inletAnalysisYearService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新年来水分析
     * @description 批量更新
     * @param lists 批量更新列表 InletAnalysisYearUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<InletAnalysisYearUpdateReqVO> lists) {
        return success(inletAnalysisYearService.updateBatch(lists));
    }

    /**
     * 删除年来水分析
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        inletAnalysisYearService.delete(id);
        return success(true);
    }

    /**
     * 批量删除年来水分析
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(inletAnalysisYearService.deleteBatch(ids));
    }

    /**
     * 获得年来水分析详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<InletAnalysisYearDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<InletAnalysisYearDetailRespVO> get(@RequestParam("id") Long id) {
        InletAnalysisYearDO inletAnalysisYear = inletAnalysisYearService.get(id);
        return success(InletAnalysisYearConvert.INSTANCE.convertDetail(inletAnalysisYear));
    }

    /**
     * 年来水分析列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 InletAnalysisYearQueryReqVO
     * @return CommonResult<List<InletAnalysisYearRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<InletAnalysisYearRespVO>> getList(@RequestQueryParam InletAnalysisYearQueryReqVO queryReqVO) {
        List<InletAnalysisYearDO> list = inletAnalysisYearService.getList(queryReqVO);
        return success(InletAnalysisYearConvert.INSTANCE.convertList(list));
    }

    /**
     * 年来水分析分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 InletAnalysisYearPageReqVO
     * @return CommonResult<PageResult<InletAnalysisYearRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<InletAnalysisYearRespVO>> page(@RequestQueryParam InletAnalysisYearPageReqVO pageVO) {
        PageResult<InletAnalysisYearDO> pageResult = inletAnalysisYearService.page(pageVO);
        return success(InletAnalysisYearConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出年来水分析Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 InletAnalysisYearExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam InletAnalysisYearExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<InletAnalysisYearDO> list = inletAnalysisYearService.getList(queryReqVO);
        // 导出 Excel
        List<InletAnalysisYearExcelVO> datas = InletAnalysisYearConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "年来水分析", "xlsx"), queryReqVO.getExportSheetName(),
                                                    InletAnalysisYearExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入年来水分析模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "年来水分析-导入模版.xls", "sheet1", InletAnalysisYearExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入年来水分析Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-year:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<InletAnalysisYearExcelVO> list = ExcelUtils.read(file, InletAnalysisYearExcelVO.class);
        return success(inletAnalysisYearService.importExcel(list, isUpdate));
    }
}