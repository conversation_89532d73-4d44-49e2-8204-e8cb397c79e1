package com.yutoudev.irrigation.irr.controller.large.rainfallprocess.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 流域降雨产流过程ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RainfallProcessRunoffLargeRespVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 代码
     */
    private Long code;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 降雨过程Id
     */
    private Long processBasinId;

    /**
     * 降雨过程标签
     */
    private String processBasinLabel;

    /**
     * 本次累计降雨量（MM）
     */
    private Double rainfall;

    /**
     * 本次最大降雨量（mm）
     */
    private Double maxRainfall;

    /**
     * 最大产流量（m³/s）
     */
    private Double maxOutflow;

    /**
     * 累计产水量（万m³）
     */
    private Double yield;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 明细列表
     */
    List<RainfallProcessRunoffUhLargeRespVO> items;
}