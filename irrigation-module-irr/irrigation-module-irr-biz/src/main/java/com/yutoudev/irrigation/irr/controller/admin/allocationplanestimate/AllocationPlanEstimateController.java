package com.yutoudev.irrigation.irr.controller.admin.allocationplanestimate;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.allocationplanestimate.vo.*;
import com.yutoudev.irrigation.irr.convert.allocationplanestimate.AllocationPlanEstimateConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationplanestimate.AllocationPlanEstimateDO;
import com.yutoudev.irrigation.irr.service.allocationplanestimate.AllocationPlanEstimateService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 *
 * 配水调度计划估算
 * @description 管理后台-配水调度计划估算controller
 * <AUTHOR>
 * @time 2024-09-16 11:16:31
 *
 */
@RestController
@RequestMapping("/irr/allocation-plan-estimate")
@Validated
public class AllocationPlanEstimateController {

    private static final String MODULE_NAME = "配水调度计划估算";

    @Resource
    private AllocationPlanEstimateService<AllocationPlanEstimateDO> allocationPlanEstimateService;

    /**
     * 创建配水调度计划估算
     * @description 单个对象保存
     * @param createReqVO AllocationPlanEstimateCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody AllocationPlanEstimateCreateReqVO createReqVO) {
        return success(allocationPlanEstimateService.create(createReqVO));
    }

    /**
     * 批量创建配水调度计划估算
     * @description 多个对象保存
     * @param lists  AllocationPlanEstimateCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<AllocationPlanEstimateCreateReqVO> lists) {
        return success(allocationPlanEstimateService.createBatch(lists));
    }

    /**
     * 更新配水调度计划估算
     * @description 单个对象修改
     * @param updateReqVO AllocationPlanEstimateUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody AllocationPlanEstimateUpdateReqVO updateReqVO) {
        allocationPlanEstimateService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新配水调度计划估算
     * @description 批量更新
     * @param lists 批量更新列表 AllocationPlanEstimateUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<AllocationPlanEstimateUpdateReqVO> lists) {
        return success(allocationPlanEstimateService.updateBatch(lists));
    }

    /**
     * 删除配水调度计划估算
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        allocationPlanEstimateService.delete(id);
        return success(true);
    }

    /**
     * 批量删除配水调度计划估算
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(allocationPlanEstimateService.deleteBatch(ids));
    }

    /**
     * 获得配水调度计划估算详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<AllocationPlanEstimateDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<AllocationPlanEstimateDetailRespVO> get(@RequestParam("id") Long id) {
        AllocationPlanEstimateDO allocationPlanEstimate = allocationPlanEstimateService.get(id);
        return success(AllocationPlanEstimateConvert.INSTANCE.convertDetail(allocationPlanEstimate));
    }

    /**
     * 配水调度计划估算列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 AllocationPlanEstimateQueryReqVO
     * @return CommonResult<List<AllocationPlanEstimateRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<AllocationPlanEstimateRespVO>> getList(@RequestQueryParam AllocationPlanEstimateQueryReqVO queryReqVO) {
        List<AllocationPlanEstimateDO> list = allocationPlanEstimateService.getList(queryReqVO);
        return success(AllocationPlanEstimateConvert.INSTANCE.convertList(list));
    }

    /**
     * 配水调度计划估算分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 AllocationPlanEstimatePageReqVO
     * @return CommonResult<PageResult<AllocationPlanEstimateRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<AllocationPlanEstimateRespVO>> page(@RequestQueryParam AllocationPlanEstimatePageReqVO pageVO) {
        PageResult<AllocationPlanEstimateDO> pageResult = allocationPlanEstimateService.page(pageVO);
        return success(AllocationPlanEstimateConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出配水调度计划估算Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 AllocationPlanEstimateExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam AllocationPlanEstimateExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<AllocationPlanEstimateDO> list = allocationPlanEstimateService.getList(queryReqVO);
        // 导出 Excel
        List<AllocationPlanEstimateExcelVO> datas = AllocationPlanEstimateConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "配水调度计划估算", "xlsx"), queryReqVO.getExportSheetName(),
                                                    AllocationPlanEstimateExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入配水调度计划估算模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "配水调度计划估算-导入模版.xls", "sheet1", AllocationPlanEstimateExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入配水调度计划估算Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-plan-estimate:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<AllocationPlanEstimateExcelVO> list = ExcelUtils.read(file, AllocationPlanEstimateExcelVO.class);
        return success(allocationPlanEstimateService.importExcel(list, isUpdate));
    }
}