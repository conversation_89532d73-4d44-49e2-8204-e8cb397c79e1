package com.yutoudev.irrigation.irr.controller.large.statistics;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.large.statistics.vo.*;
import com.yutoudev.irrigation.irr.service.statistics.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

/**
 * 大屏接口统计
 *
 * <AUTHOR>
 * @description -大屏接口统计controller
 * @time 2024-09-11 12:00:18
 */
@RestController
@RequestMapping("/irr/statistics")
@Validated
public class LargeStatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 雨情统计
     *
     * @return CommonResult<RainConditionRespVO> 详情响应VO
     * @description
     */
    @GetMapping("/rain-condition")
    public CommonResult<RainConditionRespVO> getRainCondition() {
        RainConditionRespVO rainCondition = statisticsService.getRainCondition();
        return success(rainCondition);
    }

    /**
     * 短期降雨量统计
     *
     * @return CommonResult<List < ShortTermRainfallRespVO>> 详情响应VO
     * @description
     */
    @GetMapping("/short-term-rain")
    public CommonResult<List<ShortTermRainfallRespVO>> getShortTermRainfall() {
        List<ShortTermRainfallRespVO> respVOS = statisticsService.getShortTermRainfall();
        return success(respVOS);
    }

    /**
     * 用水管理
     *
     * @return CommonResult<UseWaterRespVO> 详情响应VO
     * @description
     */
    @GetMapping("/use-water")
    public CommonResult<UseWaterRespVO> getUseWater() {
        UseWaterRespVO respVOS = statisticsService.getUseWater();
        return success(respVOS);
    }

    /**
     * 用水信息统计
     *
     * @return CommonResult<UseWaterInfoRespVO> 详情响应VO
     * @description
     */
    @GetMapping("/use-water-info")
    public CommonResult<UseWaterInfoRespVO> getUseWaterInfo() {
        UseWaterInfoRespVO respVOS = statisticsService.getUseWaterInfo();
        return success(respVOS);
    }

    /**
     * 实际用水统计
     *
     * @return CommonResult<List < UseTypeWaterVO>> 详情响应VO
     * @description
     */
    @GetMapping("/actual-use-water")
    public CommonResult<List<UseTypeWaterVO>> getActualUseWater() {
        List<UseTypeWaterVO> respVOS = statisticsService.getActualUseWater();
        return success(respVOS);
    }

    /**
     * 预报-墒情统计
     *
     * @return CommonResult<SoilMoistureContentAreaRespVO> 详情响应VO
     * @description
     */
    @GetMapping("/soil-moisture-content-area")
    public CommonResult<SoilMoistureContentAreaRespVO> getSoilMoistureContentArea() {
        SoilMoistureContentAreaRespVO respVOS = statisticsService.getSoilMoistureContentArea();
        return success(respVOS);
    }

    /**
     * 墒情数据统计
     *
     * @return CommonResult<PageResult < SoilMoistureContentDataRespVO>> 详情响应VO
     * @description
     */
    @GetMapping("/soil-moisture-content-data")
    public CommonResult<PageResult<SoilMoistureContentDataRespVO>> getSoilMoistureContentData(SoilMoistureContentDataReqVO reqVO) {
        PageResult<SoilMoistureContentDataRespVO> respVOS = statisticsService.getSoilMoistureContentData(reqVO);
        return success(respVOS);
    }


    /**
     * 供用水分析
     *
     * @return CommonResult<WaterProvideAndUseRespVO> 详情响应VO
     * @description
     */
    @GetMapping("/water-provide-and-use")
    public CommonResult<WaterProvideAndUseRespVO> getWaterProvideAndUse() {
        WaterProvideAndUseRespVO respVOS = statisticsService.getWaterProvideAndUse();
        return success(respVOS);
    }

    @GetMapping("/water-supply-contrast")
    public CommonResult<List<WaterSupplyHistoryContrastRespVO>> getWaterSupplyHistoryContrast() {
        List<WaterSupplyHistoryContrastRespVO> resp = statisticsService.getWaterSupplyHistoryContrast();
        return success(resp);
    }
}
