package com.yutoudev.irrigation.irr.controller.admin.allocationplanestimate.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 配水调度计划估算创建RequestVO
 * @description 管理后台-配水调度计划估算创建RequestVO
 * <AUTHOR>
 * @time 2024-09-16 11:16:31
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AllocationPlanEstimateCreateReqVO extends AllocationPlanEstimateBaseVO {

}
