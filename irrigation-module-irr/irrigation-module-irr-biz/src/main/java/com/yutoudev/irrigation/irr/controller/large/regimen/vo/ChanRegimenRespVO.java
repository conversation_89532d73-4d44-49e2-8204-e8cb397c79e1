package com.yutoudev.irrigation.irr.controller.large.regimen.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ChanRegimenRespVO {

    /**
     * 渠道ID
     */
    private Long id;

    /**
     * 渠道名称
     */
    private String chanName;

    /**
     * 站点ID
     */
    private Long equipId;

    /**
     * 流量(立方米/秒)
     */
    private Float flow;

    /**
     * 告警水位(米)
     */
    private Float alarmWaterLevel;

    /**
     * 实时水位(米)
     */
    private Float realTimeWaterLevel;

    /**
     * 数据上报时间
     */
    private LocalDateTime reportTime;

    /**
     * 告警次数
     */
    private Integer alarmCount;
}
