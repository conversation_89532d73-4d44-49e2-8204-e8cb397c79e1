package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisdetail.InletAnalysisDetailDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;

import java.time.LocalDateTime;


/**
 *
 * 来水分析明细list查询RequestVO
 * @description 管理后台-来水分析明细list查询RequestVO，参数和 InletAnalysisDetailPageReqVO 是一致的
 * <AUTHOR>
 * @time 2024-07-23 14:33:37
 *
 */
@Data
public class InletAnalysisDetailQueryReqVO extends QueryCriteria<InletAnalysisDetailDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 统计时间
     */
    private LocalDateTime realtime;

    /**
     * 降雨量
     * 
     */
    private Double rainfall;

    /**
     * 蒸发量
     * 
     */
    private Double evaporation;

    /**
     * 渗透值
     * 
     */
    private Double penetration;

    /**
     * 径流值
     * 
     */
    private Double runoff;

    /**
     * 径流时间
     * @mock （分钟）
     */
    private Integer runoffTime;

    /**
     * 创建时间
     *
     */
    private LocalDateTime createTime;

}
