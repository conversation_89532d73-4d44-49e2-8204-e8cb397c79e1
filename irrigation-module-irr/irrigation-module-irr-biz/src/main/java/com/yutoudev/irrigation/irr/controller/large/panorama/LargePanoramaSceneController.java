package com.yutoudev.irrigation.irr.controller.large.panorama;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramascene.PanoramaSceneDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramascene.PanoramaScenePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramascene.PanoramaSceneQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramascene.PanoramaSceneRespVO;
import com.yutoudev.irrigation.irr.convert.panorama.PanoramaSceneConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.panorama.PanoramaSceneDO;
import com.yutoudev.irrigation.irr.service.panorama.PanoramaSceneService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;


/**
 * 全景图场景
 *
 * <AUTHOR>
 * @description 大屏-全景图场景controller
 * @time 2024-09-11 12:00:16
 */
@RestController
@RequestMapping("/irr/panorama-scene")
@Validated
public class LargePanoramaSceneController {


    @Resource
    private PanoramaSceneService<PanoramaSceneDO> panoramaSceneService;


    /**
     * 获得全景图场景详情
     *
     * @param id 编号 Long
     * @return CommonResult<PanoramaSceneDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    public CommonResult<PanoramaSceneDetailRespVO> get(@RequestParam("id") Long id) {
        PanoramaSceneDO panoramaScene = panoramaSceneService.get(id);
        return success(PanoramaSceneConvert.INSTANCE.convertDetail(panoramaScene));
    }

    /**
     * 全景图场景列表
     *
     * @param queryReqVO 查询条件 PanoramaSceneQueryReqVO
     * @return CommonResult<List < PanoramaSceneRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    public CommonResult<List<PanoramaSceneRespVO>> getList(@RequestQueryParam PanoramaSceneQueryReqVO queryReqVO) {
        List<PanoramaSceneDO> list = panoramaSceneService.getList(queryReqVO);
        return success(PanoramaSceneConvert.INSTANCE.convertList(list));
    }

    /**
     * 全景图场景分页
     *
     * @param pageVO 查询条件 PanoramaScenePageReqVO
     * @return CommonResult<PageResult < PanoramaSceneRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    public CommonResult<PageResult<PanoramaSceneRespVO>> page(@RequestQueryParam PanoramaScenePageReqVO pageVO) {
        PageResult<PanoramaSceneDO> pageResult = panoramaSceneService.page(pageVO);
        return success(PanoramaSceneConvert.INSTANCE.convertPage(pageResult));
    }

}