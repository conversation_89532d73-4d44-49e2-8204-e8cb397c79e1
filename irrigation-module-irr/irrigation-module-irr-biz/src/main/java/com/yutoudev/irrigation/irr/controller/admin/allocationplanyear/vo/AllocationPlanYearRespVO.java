package com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo;

import lombok.*;
import java.util.*;
import java.time.*;
  import java.math.BigDecimal;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yutoudev.irrigation.irr.controller.admin.userbase.vo.UserBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseRespVO;

/**
 *
 * 配水调度年计划ResponseVO
 * @description 管理后台-配水调度年计划ResponseVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@Data
@ToString(callSuper = true)
public class AllocationPlanYearRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 计划名称
     * 
     */
    private String name;

    /**
     * 用水用户ID
     * 
     */
    private Long userId;

    /**
     * 用水用户
     * 
     */
    private UserBaseRespVO user;
  
    /**
     * 灌溉片区ID
     * 
     */
    private Long zoneId;

    /**
     * 灌溉片区
     * 
     */
    private ZoneBaseRespVO zone;
  
    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水渠
     * 
     */
    private ChanBaseRespVO chan;
  
    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水源
     * 
     */
    private SwhsBaseRespVO swhs;
  
    /**
     * 计划供水
     * @mock （m³）
     */
    private BigDecimal supplyWater;

    /**
     * 计划时间
     * @mock （yyyy）
     */
    private String planTime;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 创建人
     * 
     */
    private String creator;

    /**
     * 创建时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    /**
     * 修改人
     * 
     */
    private String updater;

    /**
     * 修改时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 删除标志
     * 
     */
    private Boolean deleted;

    /**
     * 租户编号
     * 
     */
    private Long tenantId;
}