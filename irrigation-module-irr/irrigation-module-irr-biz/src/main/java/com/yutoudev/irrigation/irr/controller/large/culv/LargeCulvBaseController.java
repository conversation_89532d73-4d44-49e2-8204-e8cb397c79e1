package com.yutoudev.irrigation.irr.controller.large.culv;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.culvbase.vo.CulvBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.culvbase.vo.CulvBaseRespVO;
import com.yutoudev.irrigation.irr.controller.large.flum.vo.BuildingStatsRespVO;
import com.yutoudev.irrigation.irr.convert.culvbase.CulvBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.culvbase.CulvBaseDO;
import com.yutoudev.irrigation.irr.enums.BuildingEngStatsEnum;
import com.yutoudev.irrigation.irr.service.culvbase.CulvBaseService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 大屏 - 涵洞统计
 */
@RestController
@RequestMapping("/irr/culv-base")
@Validated
@Slf4j
public class LargeCulvBaseController {

    private static final String MODULE_NAME = "大屏-涵洞统计";

    @Resource
    private CulvBaseService<CulvBaseDO> culvBaseService;

    /**
     * 获取涵洞工程建设统计
     */
    @GetMapping("/building-stats")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<BuildingStatsRespVO> statisticsBuilding() {
        return success(culvBaseService.statisticsBuilding());
    }

    /**
     * 重建涵洞分页
     *
     * @param pageVO 查询条件 CulvBasePageReqVO
     */
    @GetMapping("/page-rebuilding")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<CulvBaseRespVO>> pageRebuilding(@RequestQueryParam CulvBasePageReqVO pageVO) {
        pageVO.setEngStat(BuildingEngStatsEnum.REBUILDING.getStatus());
        PageResult<CulvBaseDO> pageResult = culvBaseService.page(pageVO);
        return success(CulvBaseConvert.INSTANCE.convertPage(pageResult));
    }
}