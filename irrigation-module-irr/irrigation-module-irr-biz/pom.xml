<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yutoudev.irrigation</groupId>
        <artifactId>irrigation-module-irr</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>irrigation-module-irr-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
       灌区业务包
    </description>
    <dependencies>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-module-irr-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-module-warn-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 业务组件 -->
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-biz-operatelog</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-biz-dict</artifactId>
        </dependency>

         <!-- 定时任务-->
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-job</artifactId>
        </dependency>
        <!-- 多租户-->
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <!-- 消息队列 -->
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hikvision.ga</groupId>
            <artifactId>artemis-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-word</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-captcha-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-spring-boot-starter-biz-weixin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-module-oa-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yutoudev.irrigation</groupId>
            <artifactId>irrigation-module-archive-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- EasyPOI -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
        </dependency>
        
        <!-- JFreeChart -->
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <configuration>
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <projectName>${project.description}</projectName>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>