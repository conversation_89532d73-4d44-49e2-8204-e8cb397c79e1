package com.yutoudev.irrigation.irr.controller.admin.inletanalysismonth.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysismonth.InletAnalysisMonthDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 *
 * 月来水分析分页RequestVO
 * @description 管理后台-月来水分析分页RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:38
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisMonthPageReqVO extends PageCriteria<InletAnalysisMonthDO> {

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 月
     * 
     */
    private String month;

    /**
     * 累计降雨量
     * 
     */
    private Double rainfall;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;

}
