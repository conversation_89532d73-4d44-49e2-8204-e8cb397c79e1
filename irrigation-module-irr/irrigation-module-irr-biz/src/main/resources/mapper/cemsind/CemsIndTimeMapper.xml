<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.cemsind.CemsIndTimeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into irr_meas_ind_time(id, equip_id, central_id, dev_id, report_time, digital1, digital2, digital3,
        digital4, digital5, digital6, digital7, digital8, digital9, digital10, digital11, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.equipId}, #{entity.centralId}, #{entity.devId}, #{entity.reportTime},
            #{entity.digital1}, #{entity.digital2}, #{entity.digital3}, #{entity.digital4}, #{entity.digital5},
            #{entity.digital6}, #{entity.digital7}, #{entity.digital8}, #{entity.digital9}, #{entity.digital10},
            #{entity.digital11}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        equip_id = values(equip_id),
        central_id = values(central_id),
        dev_id = values(dev_id),
        report_time = values(report_time),
        digital1 = values(digital1),
        digital2 = values(digital2),
        digital3 = values(digital3),
        digital4 = values(digital4),
        digital5 = values(digital5),
        digital6 = values(digital6),
        digital7 = values(digital7),
        digital8 = values(digital8),
        digital9 = values(digital9),
        digital10 = values(digital10),
        digital11 = values(digital11),
        update_time = values(update_time)
    </insert>
</mapper>
