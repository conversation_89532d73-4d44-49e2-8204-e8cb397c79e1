<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.statistics.ComprehensiveSituationMapper">


    <select id="staticsChannelWaterRegimeByMeasIndDay"
            resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.ChannelWaterRegimeRespVO">
        SELECT
            measDay.id AS id,
            measDay.`day` AS `day`,
            measDay.max_water_level AS max_water_level,
            measDay.min_water_level AS min_water_level,
            measDay.avg_water_level AS avg_water_level,
            measDay.max_water_flow AS max_water_flow,
            measDay.min_water_flow AS min_water_flow,
            measDay.avg_water_flow AS avg_water_flow,
            measDay.update_time AS measure_time,
            chan.id AS chan_id,
            chan.chan_name AS chan_name,
            chan.parent_id AS parent_id,
            chan.parent_tree_id AS parent_tree_id,
            chan.swhs_id AS swhs_id,
            chan.max_warning_value AS max_warning_value,
            chan.chan_type AS chan_type,
            chan.mlg_num AS chan_mlg_num,
            chan.sort AS chan_sort,
            equip.st_name AS st_name,
            measDay.equip_id AS equip_id,
            equip.sort AS equip_sort,
            equip.dpds_num AS equip_mlg_num,
            equip.`online` AS equip_online,
            warnInfo.id AS warn_id,
            warnInfo.warn_content AS warn_content,
            warnLevel.warn_color AS warn_color,
            warnLevel.warn_level AS warn_level,
            warnLevel.warn_level_name AS warn_level_name,
            warnInfo.create_time AS warn_time
        FROM
            irr_equip_base AS equip
        LEFT JOIN (
            SELECT * FROM
                ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY equip_id ORDER BY `day` DESC ) AS rn FROM irr_meas_ind_day ) t
            WHERE
                rn = 1
            ) AS measDay ON equip.id = measDay.equip_id
        LEFT JOIN ( SELECT equip_id, chan_id FROM irr_chan_equip_query ) AS chanEquip ON chanEquip.equip_id = equip.id
        LEFT JOIN irr_chan_base AS chan ON chan.id = chanEquip.chan_id
        LEFT JOIN (
            SELECT
                t.id,
                t.create_time,
                t.equip_id,
                t.warn_level_id,
                t.warn_content
            FROM
                ew_warn_info t
            INNER JOIN (
                SELECT
                    equip_id,
                    MAX( create_time ) AS max_time
                FROM
                    ew_warn_info
                WHERE
                    equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1)
                    AND deleted = 0
                    AND create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
                GROUP BY
                    equip_id
                ) m ON t.equip_id = m.equip_id AND t.create_time = m.max_time
            ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
        WHERE
            equip.deleted = 0 AND equip.st_type = 1 AND equip.dev_type in (1201,1202,1203)
    </select>
    <select id="staticsChannelWaterRegimeByMeasIndTime"
            resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.ChannelWaterRegimeRespVO">
        SELECT
            measTime.id AS id,
            measTime.water_level AS avg_water_level,
            measTime.calc_water_flow AS avg_water_flow,
            measTime.report_time AS measure_time,
            chan.id AS chan_id,
            chan.chan_name AS chan_name,
            chan.parent_id AS parent_id,
            chan.parent_tree_id AS parent_tree_id,
            chan.swhs_id AS swhs_id,
            chan.max_warning_value AS max_warning_value,
            chan.chan_type AS chan_type,
            chan.mlg_num AS chan_mlg_num,
            chan.sort AS chan_sort,
            equip.st_name AS st_name,
            measTime.equip_id AS equip_id,
            equip.sort AS equip_sort,
            equip.dpds_num AS equip_mlg_num,
            equip.`online` AS equip_online,
            warnInfo.id AS warn_id,
            warnInfo.warn_content AS warn_content,
            warnLevel.warn_color AS warn_color,
            warnLevel.warn_level AS warn_level,
            warnLevel.warn_level_name AS warn_level_name,
            warnInfo.create_time AS warn_time
        FROM
            irr_equip_base AS equip
        LEFT JOIN (
            SELECT *  FROM
                ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY equip_id ORDER BY report_time DESC ) AS rn FROM irr_meas_ind_time
                    WHERE report_time >= NOW() - INTERVAL 10 MINUTE ) t
            WHERE
                rn = 1
            ) AS measTime ON equip.id = measTime.equip_id
        LEFT JOIN ( SELECT equip_id, chan_id FROM irr_chan_equip_query ) AS chanEquip ON chanEquip.equip_id = equip.id
        LEFT JOIN irr_chan_base AS chan ON chan.id = chanEquip.chan_id
        LEFT JOIN (
            SELECT
                t.id,
                t.create_time,
                t.warn_level_id,
                t.equip_id,
                t.warn_content
            FROM
                ew_warn_info t
            INNER JOIN (
                SELECT
                    equip_id,
                    MAX( create_time ) AS max_time
                FROM
                    ew_warn_info
                WHERE
                    equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1)
                    AND deleted = 0
                    AND create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
                GROUP BY
                    equip_id
            ) m ON t.equip_id = m.equip_id
            AND t.create_time = m.max_time
            ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
        WHERE
            equip.deleted = 0 AND equip.st_type = 1 AND equip.dev_type in (1201,1202,1203)
    </select>

    <select id="staticsReservoirWaterRegimeByMeasIndHour"
            resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.ReservoirWaterRegimeRespVO">

        SELECT
            swhs.id as swhs_id, swhs.swhs_name as swhs_name,
            swhs.flood_m8_level as flood_m8_level, swhs.flood_m4_level as flood_m4_level,
            equip.id as equip_id,
            equip.st_name as st_name,
            equip.`online` as equip_online,
            indHour.hour_time AS hour_time,
            indHour.rainfall_volume AS rainfall_volume,
            indHour.water_level AS water_level,
            indHour.reservoir_capacity AS reservoir_capacity,
            indHour.reservoir_inflow AS reservoir_inflow,
            indHour.reservoir_outflow AS reservoir_outflow,
            warnInfo.id AS warn_id,
            warnInfo.warn_content AS warn_content,
            warnLevel.warn_level AS warn_level,
            warnLevel.warn_level_name AS warn_level_name,
            warnLevel.warn_color AS warn_color,
            warnInfo.create_time AS warn_time
        FROM irr_swhs_base as swhs
            LEFT JOIN (SELECT * from irr_equip_base where deleted=0 and st_type=1 and dev_type=1102)  as equip on equip.swhs_id = swhs.id
            LEFT JOIN (  SELECT  *  FROM
                 ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY equip_id ORDER BY hour_time DESC ) AS rn FROM irr_meas_ind_hour WHERE equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1 and dev_type=1102) and hour_time >= NOW() - INTERVAL  1 HOUR )  as t
                    WHERE
                    rn = 1 ) as indHour on indHour.equip_id = equip.id
        LEFT JOIN (
            SELECT
                id,
                create_time,
                equip_id,
                warn_level_id,
                warn_content
            FROM
                ( SELECT
                    t.*,
                    ROW_NUMBER() OVER ( PARTITION BY t.equip_id ORDER BY t.create_time DESC, t.id DESC ) AS rn
                FROM
                    ew_warn_info t
                WHERE
                    t.equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1 and dev_type=1102)
                    and t.deleted = 0
                    AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
                ) AS sub
                WHERE
                rn = 1
            ) AS warnInfo ON warnInfo.equip_id = equip.id
        LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
        WHERE swhs.deleted=0
    </select>

    <select id="staticsGateMonitor"
            resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.GateMonitorRespVO">
        SELECT
            equip.id AS id,
            gr.v1 AS v1,
            gr.v2 AS v2,
            gr.v3 AS v3,
            gr.v4 AS v4,
            gr.v5 AS v5,
            gr.v6 AS v6,
            gr.v7 AS v7,
            gr.v8 AS v8,
            gr.v9 AS v9,
            gr.v10 AS v10,
            gr.v11 AS v11,
            gr.v12 AS v12,
            gr.v13 AS v13,
            gr.v14 AS v14,
            gr.`status` AS `status`,
            gr.exec_res AS exec_res,
            gr.update_time AS update_time,
            allocation.start_time AS start_time,
            allocation.end_time AS end_time,
            allocation.gate_height AS gate_height,
            allocation.gross AS gross,
            allocation.water AS allocation_water,
            allocation.flow_rate AS allocation_flow,
            chan.id AS chan_id,
            chan.chan_name AS chan_name,
            chan.parent_id AS parent_id,
            chan.parent_tree_id AS parent_tree_id,
            chan.swhs_id AS swhs_id,
            chan.chan_type AS chan_type,
            chan.mlg_num AS chan_mlg_num,
            chan.sort AS chan_sort,
            equip.id as equip_id,
            equip.st_name AS st_name,
            equip.dev_id AS dev_id,
            equip.central_id AS central_id,
            equip.dev_type AS dev_type,
            equip.pic_url AS pic_url,
            equip.`online` AS `online`,
            equip.sort AS equip_sort,
            equip.dpds_num AS equip_mlg_num,
            warnInfo.id AS warn_id,
            warnInfo.warn_content AS warn_content,
            warnLevel.warn_level AS warn_level,
            warnLevel.warn_level_name AS warn_level_name,
            warnLevel.warn_color AS warn_color,
            warnInfo.create_time AS warn_time
        FROM
            irr_equip_base AS equip
            LEFT JOIN irr_gate_realtime as gr ON gr.equip_id = equip.id AND gr.central_id = equip.central_id AND gr.dev_id = equip.dev_id
            LEFT JOIN irr_chan_equip_query AS chanEquip ON chanEquip.equip_id = equip.id
            LEFT JOIN irr_chan_base AS chan ON chan.id = chanEquip.chan_id
            LEFT JOIN (
                SELECT *
                FROM
                ( SELECT
                    t.*,
                    ROW_NUMBER() OVER ( PARTITION BY t.equip_id ORDER BY t.start_time DESC, t.id DESC ) AS rn
                FROM
                    irr_allocation_water_scheme t
                WHERE
                    start_time > NOW() AND equip_id IS NOT NULL
                ) AS sub
                WHERE
                    rn = 1
            ) as allocation ON allocation.equip_id = equip.id
            LEFT JOIN (
                SELECT
                    t.id,
                    t.create_time,
                    t.equip_id,
                    t.warn_level_id,
                    t.warn_content
                FROM
                ew_warn_info t
                INNER JOIN (
                    SELECT
                      equip_id,
                      MAX( create_time ) AS max_time
                    FROM
                      ew_warn_info
                    WHERE
                        equip_id in (SELECT id FROM irr_equip_base WHERE deleted=0 and st_type=1)
                        and deleted = 0
                        AND create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY )
                    GROUP BY
                      equip_id
                ) m ON t.equip_id = m.equip_id AND t.create_time = m.max_time
            ) AS warnInfo ON warnInfo.equip_id = equip.id
            LEFT JOIN ew_warn_level AS warnLevel ON warnLevel.id = warnInfo.warn_level_id
        WHERE
          equip.deleted = 0 AND equip.st_type = 7
    </select>

</mapper>
