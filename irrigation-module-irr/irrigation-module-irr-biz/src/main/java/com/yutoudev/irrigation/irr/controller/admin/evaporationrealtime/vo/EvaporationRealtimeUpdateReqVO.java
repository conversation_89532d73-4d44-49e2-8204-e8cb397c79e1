package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;


/**
 * 实时蒸散发量更新RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-实时蒸散发量更新RequestVO
 * @time 2024-12-20 17:34:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvaporationRealtimeUpdateReqVO extends EvaporationRealtimeBaseVO {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}