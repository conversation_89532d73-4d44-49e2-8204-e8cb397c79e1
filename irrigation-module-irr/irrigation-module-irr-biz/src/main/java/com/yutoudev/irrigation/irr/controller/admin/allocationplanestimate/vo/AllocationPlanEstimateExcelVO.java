package com.yutoudev.irrigation.irr.controller.admin.allocationplanestimate.vo;

import lombok.*;
import java.util.*;
import java.time.*;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.experimental.Accessors;

/**
 *
 * 配水调度计划估算ExcelVO
 * @description 管理后台-配水调度计划估算导出、导入ExcelVO
 * <AUTHOR>
 * @time 2024-09-16 11:16:31
 *
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class AllocationPlanEstimateExcelVO {


    /**
     * ID
     * 
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 配水计划ID
     * 
     */
    @ExcelProperty("配水计划ID")
    private Long planId;

    /**
     * 类型
     * @mock ：0，年；1，月；2，日
     */
    @ExcelProperty("类型")
    private Integer type;

    /**
     * 水源ID
     * 
     */
    @ExcelProperty("水源ID")
    private Long swhsId;

    /**
     * 水源名称
     * 
     */
    @ExcelProperty("水源名称")
    private String swhsName;

    /**
     * 水渠ID
     * 
     */
    @ExcelProperty("水渠ID")
    private Long chanId;

    /**
     * 水渠名称
     * 
     */
    @ExcelProperty("水渠名称")
    private String chanName;

    /**
     * 子级水渠ID
     * 
     */
    @ExcelProperty("子级水渠ID")
    private Long childChanId;

    /**
     * 对应当前水渠或水源出水口ID
     * 
     */
    @ExcelProperty("对应当前水渠或水源出水口ID")
    private Long gateId;

    /**
     * 出水口名称
     * 
     */
    @ExcelProperty("出水口名称")
    private String gateName;

    /**
     * 计划供水
     * @mock （m³）
     */
    @ExcelProperty("计划供水")
    private Double supplyWater;

    /**
     * 计划供水开始时间
     * 
     */
    @ExcelProperty("计划供水开始时间")
    private LocalDateTime startTime;

    /**
     * 计划供水结束时间
     * 
     */
    @ExcelProperty("计划供水结束时间")
    private LocalDateTime endTime;

    /**
     * 损耗值
     * 
     */
    @ExcelProperty("损耗值")
    private Double loss;

    /**
     * 径流时间
     * @mock （分钟）
     */
    @ExcelProperty("径流时间")
    private Long runoffTime;
}
