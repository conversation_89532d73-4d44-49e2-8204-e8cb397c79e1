package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 设备维护ResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护ResponseVO
 * @time 2024-10-27 10:33:13
 */
@Data
@ToString(callSuper = true)
public class EquipMaintenanceRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 设备ID
     */
    private Long equipId;

    /**
     * 设备名称
     */
    private String equipName;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 维护内容
     */
    private String description;

    /**
     * 维护结果
     */
    private String result;

    /**
     * 维护人员ID
     */
    private Long userId;

    /**
     * 维护人员姓名
     */
    private String userName;

    /**
     * 维护日期
     */
    private LocalDateTime maintenanceDate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}