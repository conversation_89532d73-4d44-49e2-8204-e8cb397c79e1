package com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 灌区简报 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IrrigationBriefingRespVO extends IrrigationBriefingBaseVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "简报日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime briefingDate;

    @Schema(description = "期数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer issueNumber;

    @Schema(description = "编辑单位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String editorUnit;

    @Schema(description = "报告数据范围开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime reportStartTime;

    @Schema(description = "报告数据范围结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime reportEndTime;

    @Schema(description = "报告内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String reportContent;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;
} 