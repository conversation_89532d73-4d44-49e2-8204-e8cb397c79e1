<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.inletanalysis.InletAnalysisMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <update id="updateAvgRainfallBySwhsId" >
        UPDATE irr_inlet_analysis ia
            JOIN (
            SELECT
            ROUND( AVG( rainfall ), 3 ) avg_rainfall,
            swhs_id
            FROM
            `irr_inlet_analysis`
            WHERE
            date_type = #{dateType}
            AND swhs_id IS NOT NULL
            GROUP BY
            swhs_id
            ) temp ON ia.swhs_id = temp.swhs_id
            SET ia.avg_rainfall = temp.avg_rainfall
        WHERE
            ia.date_type = #{dateType}
          AND ia.avg_rainfall = 0
          AND ia.swhs_id IS NOT NULL
    </update>

    <update id="updateAvgRainfallByChanId" >
        UPDATE irr_inlet_analysis ia
            JOIN (
            SELECT
            ROUND( AVG( rainfall ), 3 ) avg_rainfall,
            chan_id
            FROM
            `irr_inlet_analysis`
            WHERE
            date_type = #{dateType}
            AND chan_id IS NOT NULL
            GROUP BY
            chan_id
            ) temp ON ia.chan_id = temp.chan_id
            SET ia.avg_rainfall = temp.avg_rainfall
        WHERE
            ia.date_type = #{dateType}
          AND ia.avg_rainfall = 0
          AND ia.chan_id IS NOT NULL
    </update>


    <update id="updateAvgInflowBySwhsId" >
        UPDATE irr_inlet_analysis ia
            JOIN (
            SELECT
            ROUND( AVG( inflow ), 3 ) avg_inflow,
            swhs_id
            FROM
            `irr_inlet_analysis`
            WHERE
            date_type = #{dateType}
            AND swhs_id IS NOT NULL
            GROUP BY
            swhs_id
            ) temp ON ia.swhs_id = temp.swhs_id
            SET ia.avg_inflow = temp.avg_inflow
        WHERE
            ia.date_type = #{dateType}
          AND ia.avg_inflow = 0
          AND ia.swhs_id IS NOT NULL
    </update>

    <update id="updateAvgInflowByChanId" >
        UPDATE irr_inlet_analysis ia
            JOIN (
            SELECT
            ROUND( AVG( inflow ), 3 ) avg_inflow,
            chan_id
            FROM
            `irr_inlet_analysis`
            WHERE
            date_type = #{dateType}
            AND chan_id IS NOT NULL
            GROUP BY
            chan_id
            ) temp ON ia.chan_id = temp.chan_id
            SET ia.avg_inflow = temp.avg_inflow
        WHERE
            ia.date_type = #{dateType}
          AND ia.avg_inflow = 0
          AND ia.chan_id IS NOT NULL
    </update>

    <select id="sumInflowBySwhsId" resultType="java.lang.Double">
        SELECT SUM(inflow) FROM irr_inlet_analysis WHERE swhs_id = #{swhsId} AND date_type=#{dateType} AND statistics_date LIKE CONCAT(#{time},'%');
    </select>
</mapper>
