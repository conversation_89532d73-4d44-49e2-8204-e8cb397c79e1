<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndTimeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <sql id="baseColumn">
        id,
        equip_id,
        central_id,
        dev_id,
        report_time,
        water_level,
        water_speed,
        water_flow,
        water_volume,
        calc_water_flow,
        calc_water_volume,
        reservoir_outflow,
        reservoir_out_volume,
        voltage,
        update_time
    </sql>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into irr_meas_ind_time(<include refid="baseColumn"/>)
        values
        <foreach collection="entities" item="entity" separator=",">
            (
             #{entity.id},
             #{entity.equipId},
             #{entity.centralId},
             #{entity.devId},
             #{entity.reportTime},
             #{entity.waterLevel},
             #{entity.waterSpeed},
             #{entity.waterFlow},
             #{entity.waterVolume},
             #{entity.calcWaterFlow},
             #{entity.calcWaterVolume},
             #{entity.reservoirOutflow},
             #{entity.reservoirOutVolume},
             #{entity.voltage},
             #{entity.updateTime})
        </foreach>
        on duplicate key update
        equip_id = values(equip_id),
        central_id = values(central_id),
        dev_id = values(dev_id),
        water_level = values(water_level),
        water_speed = values(water_speed),
        water_flow = values(water_flow),
        water_volume = values(water_volume),
        calc_water_flow = values(calc_water_flow),
        calc_water_volume = values(calc_water_volume),
        reservoir_outflow = values(reservoir_outflow),
        reservoir_out_volume = values(reservoir_out_volume),
        voltage = values(voltage),
        update_time = values(update_time)
    </insert>

    <select id="sumCalcWaterVolume" resultType="java.lang.Double">
        SELECT SUM(calc_water_volume) FROM irr_meas_ind_time WHERE equip_id = #{equipId} AND report_time BETWEEN #{startTime} AND #{endTime};
    </select>

    <select id="listByPeriod" resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindtime.MeasIndTimeRespVO">
        SELECT
            id, equip_id,
            DATE_FORMAT(report_time, '%Y-%m-%d %H:00:00') AS report_time,
            AVG(water_level) AS water_level,
            AVG(water_speed) AS water_speed,
            AVG(water_flow) AS water_flow,
            AVG(calc_water_flow) AS calc_water_flow
        FROM
            irr_meas_ind_time
        WHERE
            equip_id = #{equipId}
          AND report_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            FLOOR((UNIX_TIMESTAMP(report_time) - UNIX_TIMESTAMP(#{startTime})) / (3600*#{intervalTime}))
        ORDER BY
            report_time;
    </select>

</mapper>
