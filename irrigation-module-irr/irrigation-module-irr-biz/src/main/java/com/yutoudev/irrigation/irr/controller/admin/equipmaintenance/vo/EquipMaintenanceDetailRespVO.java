package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo.EquipMaintenanceFileRespVO;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备维护DetailResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护DetailResponseVO
 * @time 2024-10-27 10:33:13
 */
@Data
@ToString(callSuper = true)
public class EquipMaintenanceDetailRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 设备ID
     */
    private Long equipId;

    /**
     * 设备名称
     */
    private String equipName;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 维护内容
     */
    private String description;

    /**
     * 维护结果
     */
    private String result;

    /**
     * 维护人员ID
     */
    private Long userId;

    /**
     * 维护人员名称
     */
    private String userName;
    /**
     * 维护日期
     */
    private LocalDateTime maintenanceDate;


    /**
     * 附件
     */
    private List<EquipMaintenanceFileRespVO> files;
}
