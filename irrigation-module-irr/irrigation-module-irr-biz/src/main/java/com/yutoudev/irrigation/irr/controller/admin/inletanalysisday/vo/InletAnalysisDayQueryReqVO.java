package com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisday.InletAnalysisDayDO;


/**
 *
 * 日来水分析list查询RequestVO
 * @description 管理后台-日来水分析list查询RequestVO，参数和 InletAnalysisDayPageReqVO 是一致的
 * <AUTHOR>
 * @time 2024-07-23 14:33:36
 *
 */
@Data
public class InletAnalysisDayQueryReqVO extends QueryCriteria<InletAnalysisDayDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 日
     * 
     */
    private String day;

    /**
     * 累计降雨量
     * 
     */
    private Double rainfall;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     * 
     */
    private LocalDateTime updateTime;

}
