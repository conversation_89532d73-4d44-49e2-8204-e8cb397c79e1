package com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;

/**
 *
 * 日来水分析 Base VO
 * @description 日来水分析 Base VO，提供给添加、修改、详细的子 VO 使用
 * <AUTHOR>
 * @time 2024-07-23 14:33:36
 *
 */
@Data
public class InletAnalysisDayBaseVO {

    /**
     * 集水面积ID
     * 
     */
    @NotNull(message = "集水面积ID不能为空")
    private Long catchmentId;

    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 日
     * 
     */
    private String day;

    /**
     * 累计降雨量
     * 
     */
    private Double rainfall;
}
