package com.yutoudev.irrigation.irr.controller.large.statistics.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 用水管理ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-用水管理ResponseVO
 * @time 2025-03-12
 */
@Data
@ToString(callSuper = true)
public class UseWaterRespVO {

    /**
     * 年累计用水量 万m³
     */
    private Double year;

    /**
     * 年累计同比
     */
    private Double yearYOY;


    /**
     * 月累计用水量 万m³
     */
    private Double month;

    /**
     * 月累计同比
     */
    private Double monthYOY;


    /**
     * 日用水量 万m³
     */
    private Double day;

    /**
     * 可供水量 万m³
     */
    private Double provide;


    /**
     * 保灌天数
     */
    private Integer irrigationDays;


}
