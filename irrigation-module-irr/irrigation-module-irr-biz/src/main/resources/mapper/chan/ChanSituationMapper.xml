<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.chan.ChanSituationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <update id="saveChanSituationLength" >
        UPDATE irr_chan_base t2
            JOIN (
                SELECT
                    chan_id,
                    SUM(IF(type = 0, length, 0))/1000 AS total_a,
                    SUM(IF(type = 1, length, 0))/1000 AS total_b,
                    SUM(IF(type = 2, length, 0))/1000 AS total_c,
                    SUM(IF(type = 3, length, 0))/1000 AS total_d
                FROM irr_chan_situation
                WHERE chan_id = #{chanId}
            ) t1 ON t2.id = t1.chan_id
        SET
            t2.situation_length_a = t1.total_a,
            t2.situation_length_b = t1.total_b,
            t2.situation_length_c = t1.total_c,
            t2.situation_length_d = t1.total_d
        WHERE t2.id = #{chanId}
    </update>
</mapper>
