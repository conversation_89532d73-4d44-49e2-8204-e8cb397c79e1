package com.yutoudev.irrigation.irr.controller.large.allocationWater.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 用水效率考核ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-用水效率考核ResponseVO
 * @time 2025-03-21
 */
@Data
@ToString(callSuper = true)
public class WaterEfficiencyRespVO {
    /**
     * 渠道ID
     */
    private Long chanId;
    /**
     * 渠道名称
     */
    private String chanName;
    /**
     * 配水
     */
    private Double water;
    /**
     * 定额
     */
    private Double quota;
}
