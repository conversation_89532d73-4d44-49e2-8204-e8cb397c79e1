package com.yutoudev.irrigation.irr.controller.large.regimen;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.large.regimen.vo.MonthInflowPredictionRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipcalcconfig.EquipCalcConfigDO;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryInflowDayDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.service.equipcalcconfig.EquipCalcConfigService;
import com.yutoudev.irrigation.irr.service.history.HistoryInflowDayService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndHourService;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsBaseService;
import com.yutoudev.irrigation.irr.service.waterdatacalc.ReservoirDataCalcService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/month-inflow")
@Validated
public class LargeMonthInflowPredictionController {

    private static final String MODULE_NAME = "大屏-30天来水分析";

    @Resource
    private SwhsBaseService<SwhsBaseDO> swhsBaseService;

    @Resource
    private HistoryInflowDayService<HistoryInflowDayDO> historyInflowDayService;

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Resource
    private ReservoirDataCalcService reservoirDataCalcService;

    @Resource
    private EquipCalcConfigService<EquipCalcConfigDO> equipCalcConfigService;

    @GetMapping("/get-prediction")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<MonthInflowPredictionRespVO> getPrediction() {
        MonthInflowPredictionRespVO vo = new MonthInflowPredictionRespVO();
        EquipCalcConfigDO config = equipCalcConfigService.getMainWaterSourceConfig();
        EquipBaseCacheVO equip = reservoirDataCalcService.getMainWaterSourceEquip();
        LocalDateTime endTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime startTime = endTime.minusDays(30);
        List<MeasIndHourDO> hourDataList = measIndHourService.getListByEquipAndDateRange(equip, startTime, endTime);
        double sum = hourDataList.stream().mapToDouble(MeasIndHourDO::getReservoirInflowVolume).sum();
        double inFlowVolume = convert(sum, 4);
        // 同比去年
        LocalDateTime lastYearEndTime = endTime.minusYears(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime lastYearStartTime = lastYearEndTime.minusDays(30);
        double lastYearInFlowVolume = historyInflowDayService.getInFlowVolumeByDataRange(config.getWaterSourceId(), lastYearStartTime, lastYearEndTime);
        vo.setInflowVolume30Day((float) inFlowVolume);
        if (lastYearInFlowVolume == 0) {
            vo.setContrastPercent("-");
        } else {
            vo.setContrastPercent(BigDecimal.valueOf(inFlowVolume / lastYearInFlowVolume).setScale(2, RoundingMode.HALF_UP) + "%");
        }

        return success(vo);
    }
}
