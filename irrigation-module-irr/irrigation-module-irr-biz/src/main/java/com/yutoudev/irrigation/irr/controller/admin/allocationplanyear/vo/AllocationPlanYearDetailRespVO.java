package com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.*;
    import java.math.BigDecimal;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;
import com.yutoudev.irrigation.irr.controller.admin.userbase.vo.UserBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseRespVO;

/**
 *
 * 配水调度年计划DetailResponseVO
 * @description 管理后台-配水调度年计划DetailResponseVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@Data
@ToString(callSuper = true)
public class AllocationPlanYearDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 计划名称
     * 
     */
    private String name;

    /**
     * 用水用户ID
     * 
     */
    private Long userId;

    /**
     * 用水用户
     * 
     */
    private UserBaseRespVO user;

    /**
     * 灌溉片区ID
     * 
     */
    private Long zoneId;

    /**
     * 灌溉片区
     * 
     */
    private ZoneBaseRespVO zone;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水渠
     * 
     */
    private ChanBaseRespVO chan;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水源
     * 
     */
    private SwhsBaseRespVO swhs;

    /**
     * 计划供水
     * @mock （m³）
     */
    private BigDecimal supplyWater;

    /**
     * 计划时间
     * @mock （yyyy）
     */
    private String planTime;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 创建时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;
}
