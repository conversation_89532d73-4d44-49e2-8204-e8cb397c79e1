package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.evaporationrealtime.EvaporationRealtimeDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 实时蒸散发量分页RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-实时蒸散发量分页RequestVO
 * @time 2024-12-20 17:34:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvaporationRealtimePageReqVO extends PageCriteria<EvaporationRealtimeDO> {

    /**
     * 时间
     */
    private LocalDateTime markTime;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 水面蒸发值
     */
    private Double evaporationValue;

}
