package com.yutoudev.irrigation.irr.controller.admin.waterdiversioninversion.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 引水量反演ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class WaterDiversionInversionRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 配水计划ID，多个以逗号隔开
     */
    private String planId;

    /**
     * 水源ID
     */
    private Long swhsId;

    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;

    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 配水量
     */
    private Double water;

    /**
     * 配水流量
     */
    private Double flowRate;

    /**
     * 子级水渠ID
     */
    private Long childChanId;

    /**
     * 上级水渠ID
     */
    private Long parentChanId;

    /**
     * 取水水口ID
     */
    private Long quGateId;

    /**
     * 取水站点ID
     */
    private Long quEquipId;

    /**
     * 出水水口ID
     */
    private Long chuGateId;

    /**
     * 出水站点ID
     */
    private Long chuEquipId;

    /**
     * 出水设备到进水设备水渠长度
     */
    private Long chuQuChanLength;

    /**
     * 渠道水深
     */
    private Double waterLevel;

    /**
     * 渠道流速(基流+损耗)
     */
    private Double chanFlow;

    /**
     * 反演基流
     */
    private Double baseFlow;

    /**
     * 渗水损失
     */
    private Double secondLoss;

    /**
     * 蒸发水量
     */
    private Double evaporation;

    /**
     * 毛水流量
     */
    private Double loss;

    /**
     * 毛水量
     */
    private Double gross;

    /**
     * 径流时间
     */
    private Long runoffTime;

    /**
     * 设备开闸高度
     */
    private Double gateHeight;

    /**
     * 设备默认高度
     */
    private Double gateDefaultHeight;

    /**
     * 反演情况
     *
     * @mock 0异常，1通过
     */
    private Integer status;

    /**
     * 情况说明
     */
    private String reason;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}