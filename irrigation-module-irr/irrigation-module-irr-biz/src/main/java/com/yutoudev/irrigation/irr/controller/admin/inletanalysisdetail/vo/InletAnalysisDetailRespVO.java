package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 来水分析明细ResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-来水分析明细ResponseVO
 * @time 2024-07-23 14:33:37
 */
@Data
@ToString(callSuper = true)
public class InletAnalysisDetailRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 集水面积ID
     */
    private Long catchmentId;
    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 统计时间
     */
    private LocalDateTime realtime;

    /**
     * 降雨量
     */
    private Double rainfall;

    /**
     * 蒸发量
     */
    private Double evaporation;

    /**
     * 损耗值
     */
    private Double loss;

    /**
     * 渗透值
     */
    private Double penetration;

    /**
     * 径流值
     */
    private Double runoff;

    /**
     * 径流时间
     *
     * @mock （分钟）
     */
    private Integer runoffTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}