package com.yutoudev.irrigation.irr.controller.admin.inletanalysismonth.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 月来水分析 Base VO
 *
 * <AUTHOR>
 * @description 月来水分析 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-07-23 14:33:38
 */
@Data
public class InletAnalysisMonthBaseVO {

    /**
     * 集水面积ID
     */
    @NotNull(message = "集水面积ID不能为空")
    private Long catchmentId;
    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 月
     */
    private String month;

    /**
     * 累计降雨量
     */
    private Double rainfall;
}
