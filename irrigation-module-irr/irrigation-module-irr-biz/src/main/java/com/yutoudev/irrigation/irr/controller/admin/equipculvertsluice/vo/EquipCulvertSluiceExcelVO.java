package com.yutoudev.irrigation.irr.controller.admin.equipculvertsluice.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.yutoudev.irrigation.framework.excel.core.annotations.DictFormat;
import com.yutoudev.irrigation.framework.excel.core.convert.DictConvert;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 站点涵闸配置ExcelVO
 * @description 管理后台-站点涵闸配置导出、导入ExcelVO
 * <AUTHOR>
 * @time 2024-08-17 23:16:50
 *
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class EquipCulvertSluiceExcelVO {


    /**
     * ID
     * 
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 涵闸类型
     * @description 使用了DictConvert，根据irr_culvert_sluice_type定义，确定该值范围
     * 
     */
    @ExcelProperty(value = "涵闸类型", converter = DictConvert.class)
    // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    @DictFormat("irr_culvert_sluice_type")
    private Integer gateType;

    /**
     * 涵闸分组
     * @description 使用了DictConvert，根据irr_culvert_sluice_group定义，确定该值范围
     * 
     */
    @ExcelProperty(value = "涵闸分组", converter = DictConvert.class)
    // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    @DictFormat("irr_culvert_sluice_group")
    private Integer gateGroup;

    /**
     * 墙面类型
     * @description 使用了DictConvert，根据irr_culvert_sluice_wall_type定义，确定该值范围
     * 
     */
    @ExcelProperty(value = "墙面类型", converter = DictConvert.class)
    // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    @DictFormat("irr_culvert_sluice_wall_type")
    private Integer wallType;

    /**
     * 闸涵孔宽
     * @mock 米
     */
    @ExcelProperty("闸涵孔宽")
    private Double holeWidth;

    /**
     * 涵洞孔高
     * @mock 米
     */
    @ExcelProperty("涵洞孔高")
    private Double holeHeight;

    /**
     * 圆管的内半径
     * 
     */
    @ExcelProperty("圆管的内半径")
    private Double innerRadius;

    /**
     * 扇形闸门半径
     * 
     */
    @ExcelProperty("扇形闸门半径")
    private Double fanGateRadius;

    /**
     * 扇形闸门转动轴心距闸床高度
     * 
     */
    @ExcelProperty("扇形闸门转动轴心距闸床高度")
    private Double fanGcgbHeight;
}
