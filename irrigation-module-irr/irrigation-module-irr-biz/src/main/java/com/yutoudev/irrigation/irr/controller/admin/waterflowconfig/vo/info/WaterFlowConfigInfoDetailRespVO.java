package com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.info;

import lombok.*;

/**
 *
 * 水流配置信息DetailResponseVO
 * @description 管理后台-水流配置信息DetailResponseVO
 * <AUTHOR>
 * @time 2024-08-13 15:15:34
 *
 */
@Data
@ToString(callSuper = true)
public class WaterFlowConfigInfoDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 水流配置ID
     * 
     */
    private Long waterFlowId;

    /**
     * 月
     * 
     */
    private String month;

    /**
     * 蒸发量
     * @mock mm/m²
     */
    private Double evaporation;

    /**
     * 损耗值
     * @mock 百分比
     */
    private Double loss;

    /**
     * 径流值
     * @mock 百分比
     */
    private Double runoff;

    /**
     * 径流时间
     * @mock 分钟
     */
    private Integer runoffTime;
}
