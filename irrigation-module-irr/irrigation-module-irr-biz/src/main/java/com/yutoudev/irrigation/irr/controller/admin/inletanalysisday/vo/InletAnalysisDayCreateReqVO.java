package com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 日来水分析创建RequestVO
 * @description 管理后台-日来水分析创建RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:36
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisDayCreateReqVO extends InletAnalysisDayBaseVO {

}
