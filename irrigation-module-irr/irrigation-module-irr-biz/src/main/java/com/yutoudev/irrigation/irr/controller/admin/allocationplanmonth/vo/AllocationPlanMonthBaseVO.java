package com.yutoudev.irrigation.irr.controller.admin.allocationplanmonth.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import java.math.BigDecimal;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;

/**
 *
 * 配水调度月计划 Base VO
 * @description 配水调度月计划 Base VO，提供给添加、修改、详细的子 VO 使用
 * <AUTHOR>
 * @time 2024-07-23 14:54:39
 *
 */
@Data
public class AllocationPlanMonthBaseVO {

    /**
     * 计划名称
     * 
     */
    private String name;

    /**
     * 用水用户ID
     * 
     */
    private Long userId;



    /**
     * 灌溉片区ID
     * 
     */
    private Long zoneId;


    /**
     * 水渠ID
     * 
     */
    @NotNull(message = "水渠ID不能为空")
    private Long chanId;


    /**
     * 水源ID
     * 
     */
    @NotNull(message = "水源ID不能为空")
    private Long swhsId;

    /**
     * 计划供水
     * @mock （m³）
     */
    private BigDecimal supplyWater;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 计划时间
     * @mock （yyyy-MM）
     */
    private String planTime;
}
