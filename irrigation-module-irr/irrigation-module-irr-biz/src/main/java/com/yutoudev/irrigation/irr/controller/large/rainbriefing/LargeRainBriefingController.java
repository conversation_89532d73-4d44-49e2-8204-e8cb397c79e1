package com.yutoudev.irrigation.irr.controller.large.rainbriefing;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.rainbriefing.vo.*;
import com.yutoudev.irrigation.irr.controller.large.rainbriefing.vo.RainBriefingLargeRespVO;
import com.yutoudev.irrigation.irr.convert.rainbriefing.RainBriefingConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.rainbriefing.RainBriefingDO;
import com.yutoudev.irrigation.irr.service.rainbriefing.RainBriefingService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 雨情简报
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/rain-briefing")
@Validated
public class LargeRainBriefingController {

    private static final String MODULE_NAME = "雨情简报";

    @Resource
    private RainBriefingService<RainBriefingDO> rainBriefingService;

    /**
     * 雨情简报列表
     *
     * @param queryReqVO 查询条件 RainBriefingQueryReqVO
     * @return CommonResult<List < RainBriefingRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<RainBriefingLargeRespVO>> getList(@RequestQueryParam RainBriefingQueryReqVO queryReqVO) {
        List<RainBriefingDO> list = rainBriefingService.getList(queryReqVO);
        return success(RainBriefingConvert.INSTANCE.convertListToLarge(list));
    }

    /**
     * 雨情简报分页
     *
     * @param pageVO 查询条件 RainBriefingPageReqVO
     * @return CommonResult<PageResult < RainBriefingRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<RainBriefingLargeRespVO>> page(@RequestQueryParam RainBriefingPageReqVO pageVO) {
        PageResult<RainBriefingDO> pageResult = rainBriefingService.page(pageVO);
        return success(RainBriefingConvert.INSTANCE.convertPageToLarge(pageResult));
    }

}