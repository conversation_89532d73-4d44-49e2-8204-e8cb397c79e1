package com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing.vo.*;
import com.yutoudev.irrigation.irr.convert.irrigationbriefing.IrrigationBriefingConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.irrigationbriefing.IrrigationBriefingDO;
import com.yutoudev.irrigation.irr.service.irrigationbriefing.IrrigationBriefingService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

/**
 * 灌区简报
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/irrigation-briefing")
@Validated
public class IrrigationBriefingController {

    private static final String MODULE_NAME = "灌区简报";

    @Resource
    private IrrigationBriefingService<IrrigationBriefingDO> irrigationBriefingService;

    /**
     * 创建灌区简报
     *
     * @param createReqVO IrrigationBriefingCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:irrigation-briefing:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody IrrigationBriefingCreateReqVO createReqVO) {
        return success(irrigationBriefingService.create(createReqVO));
    }


    /**
     * 更新灌区简报
     *
     * @param updateReqVO IrrigationBriefingUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:irrigation-briefing:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody IrrigationBriefingUpdateReqVO updateReqVO) {
        irrigationBriefingService.update(updateReqVO);
        return success(true);
    }


    /**
     * 删除灌区简报
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:irrigation-briefing:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        irrigationBriefingService.delete(id);
        return success(true);
    }

    /**
     * 获得灌区简报详情
     *
     * @param id 编号 Long
     * @return CommonResult<IrrigationBriefingRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:irrigation-briefing:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<IrrigationBriefingRespVO> get(@RequestParam("id") Long id) {
        IrrigationBriefingDO irrigationBriefing = irrigationBriefingService.get(id);
        return success(IrrigationBriefingConvert.INSTANCE.convert(irrigationBriefing));
    }

    /**
     * 灌区简报列表
     *
     * @param queryReqVO 查询条件 IrrigationBriefingQueryReqVO
     * @return CommonResult<List<IrrigationBriefingRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:irrigation-briefing:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<IrrigationBriefingRespVO>> getList(@RequestQueryParam IrrigationBriefingQueryReqVO queryReqVO) {
        List<IrrigationBriefingDO> list = irrigationBriefingService.getList(queryReqVO);
        return success(IrrigationBriefingConvert.INSTANCE.convertList(list));
    }

    /**
     * 灌区简报分页
     *
     * @param pageVO 查询条件 IrrigationBriefingPageReqVO
     * @return CommonResult<PageResult<IrrigationBriefingRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:irrigation-briefing:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<IrrigationBriefingRespVO>> page(@RequestQueryParam IrrigationBriefingPageReqVO pageVO) {
        PageResult<IrrigationBriefingDO> pageResult = irrigationBriefingService.page(pageVO);
        return success(IrrigationBriefingConvert.INSTANCE.convertPage(pageResult));
    }

}
