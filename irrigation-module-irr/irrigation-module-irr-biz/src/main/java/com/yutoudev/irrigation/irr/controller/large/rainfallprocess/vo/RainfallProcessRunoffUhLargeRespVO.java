package com.yutoudev.irrigation.irr.controller.large.rainfallprocess.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 流域降雨产流过程单位线推流计算ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RainfallProcessRunoffUhLargeRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 时间
     */
    private LocalDateTime reportTime;

    /**
     * 净雨量
     */
    private Double precipitation;

    /**
     * 出口流量
     */
    private Double paramQ;

    /**
     * 实际入库流量
     */
    private Double measureInflow;
}