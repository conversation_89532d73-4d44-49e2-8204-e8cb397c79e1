package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;

/**
 *
 * 来水分析明细执行记录 Base VO
 * @description 来水分析明细执行记录 Base VO，提供给添加、修改、详细的子 VO 使用
 * <AUTHOR>
 * @time 2024-08-09 10:49:53
 *
 */
@Data
public class InletAnalysisDetailExecuteBaseVO {

    /**
     * 集水面积ID
     * 
     */
    @NotNull(message = "集水面积ID不能为空")
    private Long catchmentId;

    /**
     * 计算时间
     * 
     */
    private LocalDateTime computTime;

    /**
     * 执行数据
     * @mock json结构，存储设备及对应降雨量
     */
    private String executeData;

    /**
     * 蒸发量
     * 
     */
    private Double evaporation;

    /**
     * 损耗值
     * 
     */
    private Double loss;

    /**
     * 径流值
     * 
     */
    private Double runoff;

    /**
     * 径流时间
     * @mock 分钟
     */
    private Integer runoffTime;

    /**
     * 执行次数
     * 
     */
    private Integer executeNum;

    /**
     * 执行状态
     * @mock 0，待计算，1，计算完成
     */
    private Integer executeStatus;

    /**
     * 执行日志
     * 
     */
    private String executeLogs;
}
