package com.yutoudev.irrigation.irr.controller.admin.allocationplanmonth.vo;

import lombok.*;

import javax.validation.constraints.*;

/**
 *
 * 配水调度月计划更新RequestVO
 * @description 管理后台-配水调度月计划更新RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:39
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AllocationPlanMonthUpdateReqVO extends AllocationPlanMonthBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;

}