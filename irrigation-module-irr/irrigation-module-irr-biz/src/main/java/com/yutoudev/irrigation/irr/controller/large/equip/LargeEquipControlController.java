package com.yutoudev.irrigation.irr.controller.large.equip;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.equipcontrol.vo.*;
import com.yutoudev.irrigation.irr.controller.large.equip.vo.EquipLargeRespVO;
import com.yutoudev.irrigation.irr.convert.equipbase.EquipBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.equipcontrol.EquipControlDisplayService;
import com.yutoudev.irrigation.irr.service.equipcontrol.EquipControlService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 设备控制相关操作
 *
 * <AUTHOR>
 * @description 大屏-设备控制controller
 * @time 2024-06-08 10:42:01
 */
@RestController
@RequestMapping("/irr/equip-control")
public class LargeEquipControlController {

    private static final String MODULE_NAME = "设备控制";

    @Resource
    private EquipControlDisplayService equipControlDisplayService;

    @Resource
    private EquipControlService equipControlService;

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    /**
     * 设备控制页量测设备分页查询
     *
     * @param pageVO MeasControlPageReqVO
     * @return CommonResult<PageResult < MeasControlRespVO>> 返回成功
     * @description 设备控制页量测设备分页查询
     */
    @GetMapping("/meas/page")
    public CommonResult<PageResult<MeasControlRespVO>> page(@RequestQueryParam MeasControlPageReqVO pageVO) {
        PageResult<MeasControlRespVO> pageResult = equipControlDisplayService.pageMeas(pageVO);
        return success(pageResult);
    }

    /**
     * 设备控制页量测设备查询
     *
     * @param pageVO MeasControlPageReqVO
     * @return CommonResult<List < MeasControlRespVO>> 返回成功
     * @description 设备控制页量测设备查询
     */
    @GetMapping("/meas/list")
    public CommonResult<List<MeasControlRespVO>> list(@RequestQueryParam MeasControlPageReqVO pageVO) {
        List<MeasControlRespVO> resultList = equipControlDisplayService.listMeas(pageVO);
        return success(resultList);
    }


    /**
     * 设备控制页闸控设备分页查询
     *
     * @param pageVO GateControlPageReqVO
     * @return CommonResult<PageResult < GateControlRespVO>> 返回成功
     * @description 设备控制页闸控设备分页查询
     */
    @GetMapping("/gate/page")
    public CommonResult<PageResult<GateControlRespVO>> page(@RequestQueryParam EquipControlPageReqVO pageVO) {
        PageResult<GateControlRespVO> pageResult = equipControlDisplayService.pageGate(pageVO);
        return success(pageResult);
    }


    /**
     * 设备控制页闸控设备分页查询
     *
     * @param pageVO GateControlPageReqVO
     * @return CommonResult<PageResult < GateControlRespVO>> 返回成功
     * @description 设备控制页闸控设备分页查询
     */
    @GetMapping("/gate/list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<List<GateControlRespVO>> list(@RequestQueryParam EquipControlPageReqVO pageVO) {
        List<GateControlRespVO> pageResult = equipControlDisplayService.listGate(pageVO);
        return success(pageResult);
    }

    /**
     * 刷新设备最新数据
     *
     * @param req GateControlReqVO
     * @return CommonResult<Boolean> 返回成功
     * @description 查询设备最新数据
     */
    @PostMapping("/refreshData")
    public CommonResult<Boolean> refreshData(@Validated @RequestBody EquipControlReqVO req) {
        return success(equipControlService.refreshData(req));
    }

    /**
     * 站点管理精简对象列表
     *
     * @param queryReqVO 查询条件 EquipBaseQueryReqVO
     * @return CommonResult<List < EquipBaseRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/camera-list")
    public CommonResult<List<EquipLargeRespVO>> getCameraList(@RequestQueryParam EquipBaseQueryReqVO queryReqVO) {
        List<EquipBaseDO> list = equipBaseService.getCameraListOfManage(queryReqVO);
        return success(EquipBaseConvert.INSTANCE.convertListToLarge(list));
    }

}
