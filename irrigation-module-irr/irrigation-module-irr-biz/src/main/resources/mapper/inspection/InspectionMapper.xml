<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.inspection.InspectionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="getTotalLength" resultType="java.lang.Float">
        select sum(cb.chan_len) total_chan_len
        from irr_inspection ins
                 left join irr_chan_base cb on ins.chan_id = cb.id
        <![CDATA[
        where ins.status = 2
          and ins.end_time >= #{startTime}
          and ins.end_time <= #{endTime}
        ]]>
    </select>
    <select id="selectYearInspectionStats" resultType="java.lang.Long">
        select count(1) inspection_count from irr_inspection where status = 2 and YEAR (end_time) = YEAR (CURDATE())
    </select>
</mapper>
