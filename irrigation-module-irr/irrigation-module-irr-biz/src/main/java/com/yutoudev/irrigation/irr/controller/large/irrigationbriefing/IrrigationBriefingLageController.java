package com.yutoudev.irrigation.irr.controller.large.irrigationbriefing;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.api.irrigationbriefing.dto.IrrigationBriefingLagePageReqDTO;
import com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing.vo.IrrigationBriefingPageReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.irrigationbriefing.IrrigationBriefingDO;
import com.yutoudev.irrigation.irr.service.irrigationbriefing.IrrigationBriefingService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

/**
 * 档案大屏接口
 */
@RestController
@RequestMapping("/irr/large-irrigation-briefing")
@Validated
public class IrrigationBriefingLageController {

    @Resource
    private IrrigationBriefingService<IrrigationBriefingDO> irrigationBriefingApi;

    @GetMapping("/page")
    public CommonResult<PageResult<IrrigationBriefingDO>> page(@RequestQueryParam IrrigationBriefingLagePageReqDTO pageVO) {
        IrrigationBriefingPageReqVO req = new IrrigationBriefingPageReqVO();
        req.setPageNo(pageVO.getPageNo());
        req.setPageSize(pageVO.getPageSize());
        PageResult<IrrigationBriefingDO> pageResult = irrigationBriefingApi.page(req);
        return success(pageResult);
    }
}
