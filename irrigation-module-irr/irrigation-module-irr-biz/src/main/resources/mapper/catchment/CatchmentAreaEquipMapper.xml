<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.catchment.CatchmentAreaEquipMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <update id="updateRatioByCatchmentId">
        UPDATE irr_catchment_area_equip e
            LEFT JOIN (
            SELECT
            id,
            area,
            CONCAT(
            FORMAT( area /( SELECT SUM( area ) FROM `irr_catchment_area_equip` WHERE catchment_id = #{catchmentId} ), 3 )) AS percentage
            FROM
            irr_catchment_area_equip
            WHERE
            catchment_id = #{catchmentId}
            ) t ON e.id = t.id
            SET e.ratio = t.percentage
        WHERE
            e.catchment_id = #{catchmentId}
    </update>
</mapper>
