package com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 设备维护附件DetailResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护附件DetailResponseVO
 * @time 2024-10-27 10:33:14
 */
@Data
@ToString(callSuper = true)
public class EquipMaintenanceFileDetailRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 设备维护ID
     */
    private Long maintenanceId;

    /**
     * 附件id
     */
    private Long fileId;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 附件url
     */
    private String fileUrl;

    /**
     * 附件媒体类型
     */
    private String fileType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
