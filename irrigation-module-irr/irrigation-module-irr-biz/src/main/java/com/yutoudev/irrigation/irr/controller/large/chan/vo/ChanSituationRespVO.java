package com.yutoudev.irrigation.irr.controller.large.chan.vo;

import lombok.Data;

/**
 * 大屏 - 水渠评价情况统计 Response VO
 *
 * <AUTHOR>
 */
@Data
public class ChanSituationRespVO {
    
    /**
     * 水渠ID
     */
    private Long id;
    
    /**
     * 水渠名称
     */
    private String chanName;
    
    /**
     * 水渠长度(km)
     */
    private Double chanLen;

    /**
     * 情况A类长度(km)
     */
    private Double situationLengthA;

    /**
     * 情况B类长度(km)
     */
    private Double situationLengthB;

    /**
     * 情况C类长度(km)
     */
    private Double situationLengthC;

    /**
     * 情况D类长度(km)
     */
    private Double situationLengthD;

    /**
     * 现状建筑物完好率(%) （A）/总数 这里保留的4位小数，方便显示百分比
     */
    private Double intactRate;
}