package com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo;

import lombok.*;

import javax.validation.constraints.*;

import com.yutoudev.irrigation.irr.controller.admin.userbase.vo.UserBaseUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseUpdateReqVO;

/**
 *
 * 配水调度年计划更新RequestVO
 * @description 管理后台-配水调度年计划更新RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AllocationPlanYearUpdateReqVO extends AllocationPlanYearBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 用水用户
     * @description 关联对象
     * 
     */
    private UserBaseUpdateReqVO user;
    /**
     * 灌溉片区
     * @description 关联对象
     * 
     */
    private ZoneBaseUpdateReqVO zone;
    /**
     * 水渠
     * @description 关联对象
     * 
     */
    private ChanBaseUpdateReqVO chan;
    /**
     * 水源
     * @description 关联对象
     * 
     */
    @NotNull(message = "水源不能为空")
    private SwhsBaseUpdateReqVO swhs;
}