<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.coef.CoefInstantaneousMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectDataMatrix"
            resultType="com.yutoudev.irrigation.irr.controller.admin.coef.vo.instantaneous.CoefInstantaneousMatrixRespVO">
        SELECT
        param_tk ,
        MAX(CASE WHEN param_n = 1.0 THEN coef END) AS `n10`,
        MAX(CASE WHEN param_n = 1.1 THEN coef END) AS `n11`,
        MAX(CASE WHEN param_n = 1.2 THEN coef END) AS `n12`,
        MAX(CASE WHEN param_n = 1.3 THEN coef END) AS `n13`,
        MAX(CASE WHEN param_n = 1.4 THEN coef END) AS `n14`,
        MAX(CASE WHEN param_n = 1.5 THEN coef END) AS `n15`,
        MAX(CASE WHEN param_n = 1.6 THEN coef END) AS `n16`,
        MAX(CASE WHEN param_n = 1.7 THEN coef END) AS `n17`,
        MAX(CASE WHEN param_n = 1.8 THEN coef END) AS `n18`,
        MAX(CASE WHEN param_n = 1.9 THEN coef END) AS `n19`,
        MAX(CASE WHEN param_n = 2.0 THEN coef END) AS `n20`,
        MAX(CASE WHEN param_n = 2.1 THEN coef END) AS `n21`,
        MAX(CASE WHEN param_n = 2.2 THEN coef END) AS `n22`,
        MAX(CASE WHEN param_n = 2.3 THEN coef END) AS `n23`,
        MAX(CASE WHEN param_n = 2.4 THEN coef END) AS `n24`,
        MAX(CASE WHEN param_n = 2.5 THEN coef END) AS `n25`,
        MAX(CASE WHEN param_n = 2.6 THEN coef END) AS `n26`,
        MAX(CASE WHEN param_n = 2.7 THEN coef END) AS `n27`,
        MAX(CASE WHEN param_n = 2.8 THEN coef END) AS `n28`,
        MAX(CASE WHEN param_n = 2.9 THEN coef END) AS `n29`,
        MAX(CASE WHEN param_n = 3.0 THEN coef END) AS `n30`,
        MAX(CASE WHEN param_n = 3.1 THEN coef END) AS `n31`,
        MAX(CASE WHEN param_n = 3.2 THEN coef END) AS `n32`,
        MAX(CASE WHEN param_n = 3.3 THEN coef END) AS `n33`,
        MAX(CASE WHEN param_n = 3.4 THEN coef END) AS `n34`,
        MAX(CASE WHEN param_n = 3.5 THEN coef END) AS `n35`,
        MAX(CASE WHEN param_n = 3.6 THEN coef END) AS `n36`,
        MAX(CASE WHEN param_n = 3.7 THEN coef END) AS `n37`,
        MAX(CASE WHEN param_n = 3.8 THEN coef END) AS `n38`,
        MAX(CASE WHEN param_n = 3.9 THEN coef END) AS `n39`,
        MAX(CASE WHEN param_n = 4.0 THEN coef END) AS `n40`,
        MAX(CASE WHEN param_n = 4.1 THEN coef END) AS `n41`,
        MAX(CASE WHEN param_n = 4.2 THEN coef END) AS `n42`,
        MAX(CASE WHEN param_n = 4.3 THEN coef END) AS `n43`,
        MAX(CASE WHEN param_n = 4.4 THEN coef END) AS `n44`,
        MAX(CASE WHEN param_n = 4.5 THEN coef END) AS `n45`,
        MAX(CASE WHEN param_n = 4.6 THEN coef END) AS `n46`,
        MAX(CASE WHEN param_n = 4.7 THEN coef END) AS `n47`,
        MAX(CASE WHEN param_n = 4.8 THEN coef END) AS `n48`,
        MAX(CASE WHEN param_n = 4.9 THEN coef END) AS `n49`,
        MAX(CASE WHEN param_n = 5.0 THEN coef END) AS `n50`,
        MAX(CASE WHEN param_n = 5.1 THEN coef END) AS `n51`,
        MAX(CASE WHEN param_n = 5.2 THEN coef END) AS `n52`,
        MAX(CASE WHEN param_n = 5.3 THEN coef END) AS `n53`,
        MAX(CASE WHEN param_n = 5.4 THEN coef END) AS `n54`,
        MAX(CASE WHEN param_n = 5.5 THEN coef END) AS `n55`,
        MAX(CASE WHEN param_n = 5.6 THEN coef END) AS `n56`,
        MAX(CASE WHEN param_n = 5.7 THEN coef END) AS `n57`,
        MAX(CASE WHEN param_n = 5.8 THEN coef END) AS `n58`,
        MAX(CASE WHEN param_n = 5.9 THEN coef END) AS `n59`,
        MAX(CASE WHEN param_n = 6.0 THEN coef END) AS `n60`,
        MAX(CASE WHEN param_n = 6.1 THEN coef END) AS `n61`,
        MAX(CASE WHEN param_n = 6.2 THEN coef END) AS `n62`,
        MAX(CASE WHEN param_n = 6.3 THEN coef END) AS `n63`,
        MAX(CASE WHEN param_n = 6.4 THEN coef END) AS `n64`,
        MAX(CASE WHEN param_n = 6.5 THEN coef END) AS `n65`,
        MAX(CASE WHEN param_n = 6.6 THEN coef END) AS `n66`,
        MAX(CASE WHEN param_n = 6.7 THEN coef END) AS `n67`,
        MAX(CASE WHEN param_n = 6.8 THEN coef END) AS `n68`,
        MAX(CASE WHEN param_n = 6.9 THEN coef END) AS `n69`,
        MAX(CASE WHEN param_n = 7.0 THEN coef END) AS `n70`
        FROM
        irr_coef_instantaneous
        GROUP BY
        param_tk
        ORDER BY
        param_tk;
    </select>
</mapper>
