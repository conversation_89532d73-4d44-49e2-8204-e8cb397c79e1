package com.yutoudev.irrigation.irr.controller.large.allocationWater;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.allocation.vo.planbatch.AllocationPlanBatchDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterRespVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.AllocationWaterSchedulingRespVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.scheme.AllocationWaterSchemePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.scheme.AllocationWaterSchemeRespVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.schemegate.AllocationWaterSchemeGatePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.schemegate.AllocationWaterSchemeGateQueryReqVO;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterEfficiencyRespVO;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterPlanChanInfoRespVO;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterPlanReqVO;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterPlanRespVO;
import com.yutoudev.irrigation.irr.convert.allocation.AllocationPlanBatchConvert;
import com.yutoudev.irrigation.irr.convert.allocationwater.AllocationWaterConvert;
import com.yutoudev.irrigation.irr.convert.allocationwater.AllocationWaterSchemeConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.allocation.AllocationPlanBatchDO;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationwater.AllocationWaterDO;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationwater.AllocationWaterSchemeDO;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationwater.AllocationWaterSchemeGateDO;
import com.yutoudev.irrigation.irr.service.allocation.AllocationPlanBatchService;
import com.yutoudev.irrigation.irr.service.allocationwater.AllocationWaterSchemeGateService;
import com.yutoudev.irrigation.irr.service.allocationwater.AllocationWaterSchemeService;
import com.yutoudev.irrigation.irr.service.allocationwater.AllocationWaterService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 配水管理
 *
 * <AUTHOR>
 * @description 大屏接口-配水管理controller
 * @time 2025-03-21 14:09:59
 */
@RestController
@RequestMapping("/irr/allocation-water")
@Validated
public class LargeAllocationWaterController {

    private static final String MODULE_NAME = "配水管理";

    @Resource
    private AllocationWaterService<AllocationWaterDO> allocationWaterService;
    @Resource
    private AllocationWaterSchemeGateService<AllocationWaterSchemeGateDO> allocationWaterSchemeGateService;
    @Resource
    private AllocationWaterSchemeService<AllocationWaterSchemeDO> allocationWaterSchemeService;
    @Resource
    private AllocationPlanBatchService<AllocationPlanBatchDO> allocationPlanBatchService;

    /**
     * 用水效率考核
     *
     * @return CommonResult<List < WaterEfficiencyRespVO>> 列表响应VO
     */
    @GetMapping("/water-efficiency")
    public CommonResult<List<WaterEfficiencyRespVO>> getWaterEfficiency() {
        List<WaterEfficiencyRespVO> list = allocationWaterService.getWaterEfficiency();
        return success(list);
    }

    /**
     * 获得配水计划批次执行详情
     *
     * @return CommonResult<AllocationPlanBatchDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/batch/executing")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<AllocationPlanBatchDetailRespVO> getBatchExecuting() {
        AllocationPlanBatchDO allocationPlanBatch = allocationPlanBatchService.getExecuting();
        return success(AllocationPlanBatchConvert.INSTANCE.convertDetail(allocationPlanBatch));
    }

    /**
     * 配水计划（未执行完成的配水计划）
     *
     * @return CommonResult<PageResult < WaterPlanRespVO>> 列表响应VO
     */
    @GetMapping("/water-plan")
    public CommonResult<PageResult<WaterPlanRespVO>> getWaterPlan(WaterPlanReqVO reqVO) {
        PageResult<WaterPlanRespVO> list = allocationWaterService.getWaterPlan(reqVO);
        return success(list);
    }

    /**
     * 配水计划水渠配水信息（未执行完成的配水计划）
     *
     * @return CommonResult<PageResult < WaterPlanRespVO>> 列表响应VO
     */
    @GetMapping("/water-plan/chan")
    public CommonResult<List<WaterPlanChanInfoRespVO>> getWaterPlan() {
        List<WaterPlanChanInfoRespVO> list = allocationWaterService.getWaterPlanChanInfo();
        return success(list);
    }

    /**
     * 获得配水管理执行详情
     *
     * @return CommonResult<AllocationWaterDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/executing")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<AllocationWaterDetailRespVO> getExecuting() {
        AllocationWaterDO allocationWaterDO = allocationWaterService.getExecuting();
        return success(AllocationWaterConvert.INSTANCE.convertDetail(allocationWaterDO));
    }

    /**
     * 配水管理分页
     *
     * @param pageVO 查询条件 AllocationWaterPageReqVO
     * @return CommonResult<PageResult < AllocationWaterRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    public CommonResult<PageResult<AllocationWaterRespVO>> page(@RequestQueryParam AllocationWaterPageReqVO pageVO) {
        PageResult<AllocationWaterDO> pageResult = allocationWaterService.page(pageVO);
        return success(AllocationWaterConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 配水方案分页
     *
     * @param pageVO 查询条件 AllocationWaterSchemePageReqVO
     * @return CommonResult<PageResult < AllocationWaterSchemeRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/scheme/page")
    public CommonResult<PageResult<AllocationWaterSchemeRespVO>> page(@RequestQueryParam AllocationWaterSchemePageReqVO pageVO) {
        PageResult<AllocationWaterSchemeDO> pageResult = allocationWaterSchemeService.page(pageVO);
        return success(AllocationWaterSchemeConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 执行管理
     *
     * @param queryReqVO 查询条件 AllocationWaterSchemeGatePageReqVO 仅有gateId,equipId生效
     * @return CommonResult<PageResult < AllocationWaterSchedulingRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值
     */
    @GetMapping("/scheme/scheduling")
    public CommonResult<PageResult<AllocationWaterSchedulingRespVO>> scheduling(@RequestQueryParam AllocationWaterSchemeGatePageReqVO queryReqVO) {
        PageResult<AllocationWaterSchedulingRespVO> list = allocationWaterSchemeGateService.scheduling(queryReqVO.getPageNo(), queryReqVO.getPageSize(), queryReqVO.getGateId(), queryReqVO.getEquipId());
        return success(list);
    }


}