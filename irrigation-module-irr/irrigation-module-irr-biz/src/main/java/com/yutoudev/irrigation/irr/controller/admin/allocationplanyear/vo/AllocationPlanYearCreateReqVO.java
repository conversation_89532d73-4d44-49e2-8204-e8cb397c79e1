package com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo;

import lombok.*;

import com.yutoudev.irrigation.irr.controller.admin.userbase.vo.UserBaseCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseCreateReqVO;

/**
 *
 * 配水调度年计划创建RequestVO
 * @description 管理后台-配水调度年计划创建RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AllocationPlanYearCreateReqVO extends AllocationPlanYearBaseVO {

    /**
     * 用水用户
     * @description 关联对象
     * 
     */
    private UserBaseCreateReqVO user;
    /**
     * 灌溉片区
     * @description 关联对象
     * 
     */
    private ZoneBaseCreateReqVO zone;
    /**
     * 水渠
     * @description 关联对象
     * 
     */
    private ChanBaseCreateReqVO chan;
    /**
     * 水源
     * @description 关联对象
     * 
     */
    private SwhsBaseCreateReqVO swhs;
}
