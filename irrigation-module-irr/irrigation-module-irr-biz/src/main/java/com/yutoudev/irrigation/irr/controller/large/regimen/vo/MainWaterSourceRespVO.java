package com.yutoudev.irrigation.irr.controller.large.regimen.vo;

import lombok.Data;

/**
 * 主要水源信息响应VO
 * 
 * <AUTHOR>
 */
@Data
public class MainWaterSourceRespVO {

    /**
     * 实时水位(米)
     */
    private Float realTimeWaterLevel;

    /**
     * 出库流量(立方米/秒)
     */
    private Float outflow;

    /**
     * 入库流量(立方米/秒)
     */
    private Float inflow;

    /**
     * 实时蓄水量(亿立方米)
     */
    private Float realTimeStorage;

    /**
     * 校核洪水位(米)
     */
    private Float checkFloodLevel;

    /**
     * 8-10月汛限水位/正常蓄水位(米)
     */
    private Float floodM8Level;

    /**
     * 4-7月汛限水位（米）
     */
    private Float floodM4Level;
}
