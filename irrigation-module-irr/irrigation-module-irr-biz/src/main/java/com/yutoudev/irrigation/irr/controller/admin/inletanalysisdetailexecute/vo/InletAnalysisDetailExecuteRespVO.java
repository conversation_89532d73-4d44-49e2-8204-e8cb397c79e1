package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * 来水分析明细执行记录ResponseVO
 * @description 管理后台-来水分析明细执行记录ResponseVO
 * <AUTHOR>
 * @time 2024-08-09 10:49:53
 *
 */
@Data
@ToString(callSuper = true)
public class InletAnalysisDetailExecuteRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;

    /**
     * 计算时间
     * 
     */
    private LocalDateTime computTime;

    /**
     * 执行数据
     * @mock json结构，存储设备及对应降雨量
     */
    private String executeData;

    /**
     * 蒸发量
     * 
     */
    private Double evaporation;

    /**
     * 损耗值
     * 
     */
    private Double loss;

    /**
     * 径流值
     * 
     */
    private Double runoff;

    /**
     * 径流时间
     * @mock 分钟
     */
    private Integer runoffTime;

    /**
     * 执行次数
     * 
     */
    private Integer executeNum;

    /**
     * 执行状态
     * @mock 0，待计算，1，计算完成
     */
    private Integer executeStatus;

    /**
     * 执行日志
     * 
     */
    private String executeLogs;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     * 
     */
    private LocalDateTime updateTime;
}