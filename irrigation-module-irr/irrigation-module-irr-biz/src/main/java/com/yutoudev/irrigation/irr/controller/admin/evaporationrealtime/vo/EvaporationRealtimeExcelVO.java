package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 实时蒸散发量ExcelVO
 *
 * <AUTHOR>
 * @description 管理后台-实时蒸散发量导出、导入ExcelVO
 * @time 2024-12-20 17:34:41
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class EvaporationRealtimeExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 时间
     */
    @ExcelProperty("时间")
    private LocalDateTime markTime;

    /**
     * 水源地ID
     */
    @ExcelProperty("水源地ID")
    private Long swhsId;

    /**
     * 水源地名称
     */
    @ExcelProperty("水源地名称")
    private String swhsName;

    /**
     * 水面蒸发值
     */
    @ExcelProperty("水面蒸发值")
    private Double evaporationValue;
}
