package com.yutoudev.irrigation.irr.controller.large.swhs;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseRespVO;
import com.yutoudev.irrigation.irr.convert.swhsbase.SwhsBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsSpillwayDO;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsBaseService;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsSpillwayService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 水源基础信息controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/swhs-base")
@Validated
public class LargeSwhsBaseController {

    private static final String MODULE_NAME = "水源地基础信息";

    @Resource
    private SwhsBaseService<SwhsBaseDO> swhsBaseService;

    @Resource
    private SwhsSpillwayService<SwhsSpillwayDO> swhsSpillwayService;

    /**
     * 返回主水源
     *
     * @return
     */
    @GetMapping("/getMaster")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<SwhsBaseDetailRespVO> getMaster() {
        Long id = swhsBaseService.getMainReservoirId();
        SwhsBaseDO swhsBase = swhsBaseService.get(id);
        SwhsBaseDetailRespVO detailRespVO = SwhsBaseConvert.INSTANCE.convertDetail(swhsBase);
        return success(detailRespVO);
    }

    /**
     * 获得水源地基础信息详情
     *
     * @param id 编号 Long
     * @return CommonResult<SwhsBaseDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<SwhsBaseDetailRespVO> get(@RequestParam("id") Long id) {
        SwhsBaseDO swhsBase = swhsBaseService.get(id);
        SwhsBaseDetailRespVO detailRespVO = SwhsBaseConvert.INSTANCE.convertDetail(swhsBase);
        return success(detailRespVO);
    }

    /**
     * 水源地基础信息列表
     *
     * @param queryReqVO 查询条件 SwhsBaseQueryReqVO
     * @return CommonResult<List < SwhsBaseRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<SwhsBaseRespVO>> getList(@RequestQueryParam SwhsBaseQueryReqVO queryReqVO) {
        List<SwhsBaseDO> list = swhsBaseService.getList(queryReqVO);
        return success(SwhsBaseConvert.INSTANCE.convertList(list));
    }


    /**
     * 水源地基础信息分页
     *
     * @param pageVO 查询条件 SwhsBasePageReqVO
     * @return CommonResult<PageResult < SwhsBaseRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<SwhsBaseRespVO>> page(@RequestQueryParam SwhsBasePageReqVO pageVO) {
        PageResult<SwhsBaseDO> pageResult = swhsBaseService.page(pageVO);
        return success(SwhsBaseConvert.INSTANCE.convertPage(pageResult));
    }

}