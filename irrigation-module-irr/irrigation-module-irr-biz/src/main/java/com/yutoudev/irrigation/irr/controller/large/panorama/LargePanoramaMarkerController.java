package com.yutoudev.irrigation.irr.controller.large.panorama;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramamarker.PanoramaMarkerDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramamarker.PanoramaMarkerPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramamarker.PanoramaMarkerQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramamarker.PanoramaMarkerRespVO;
import com.yutoudev.irrigation.irr.convert.panorama.PanoramaMarkerConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.panorama.PanoramaMarkerDO;
import com.yutoudev.irrigation.irr.service.panorama.PanoramaMarkerService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;


/**
 * 全景图场景标记
 *
 * <AUTHOR>
 * @description 管理后台-全景图场景标记controller
 * @time 2024-09-11 12:00:18
 */
@RestController
@RequestMapping("/irr/panorama-marker")
@Validated
public class LargePanoramaMarkerController {

    @Resource
    private PanoramaMarkerService<PanoramaMarkerDO> panoramaMarkerService;

    /**
     * 获得全景图场景标记详情
     *
     * @param id 编号 Long
     * @return CommonResult<PanoramaMarkerDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    public CommonResult<PanoramaMarkerDetailRespVO> get(@RequestParam("id") Long id) {
        PanoramaMarkerDO panoramaMarker = panoramaMarkerService.get(id);
        return success(PanoramaMarkerConvert.INSTANCE.convertDetail(panoramaMarker));
    }

    /**
     * 全景图场景标记列表
     *
     * @param queryReqVO 查询条件 PanoramaMarkerQueryReqVO
     * @return CommonResult<List < PanoramaMarkerRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    public CommonResult<List<PanoramaMarkerRespVO>> getList(@RequestQueryParam PanoramaMarkerQueryReqVO queryReqVO) {
        List<PanoramaMarkerDO> list = panoramaMarkerService.getList(queryReqVO);
        return success(PanoramaMarkerConvert.INSTANCE.convertList(list));
    }

    /**
     * 全景图场景标记分页
     *
     * @param pageVO 查询条件 PanoramaMarkerPageReqVO
     * @return CommonResult<PageResult < PanoramaMarkerRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    public CommonResult<PageResult<PanoramaMarkerRespVO>> page(@RequestQueryParam PanoramaMarkerPageReqVO pageVO) {
        PageResult<PanoramaMarkerDO> pageResult = panoramaMarkerService.page(pageVO);
        return success(PanoramaMarkerConvert.INSTANCE.convertPage(pageResult));
    }

}