package com.yutoudev.irrigation.irr.controller.large.chan.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 水渠基础信息ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ChanBaseLargeRespVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 水渠代码
     */
    private String chanCode;

    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 水渠类型
     */
    private Integer chanType;

    /**
     * 桩号
     */
    private String mlgNum;

    /**
     * 起桩号
     */
    private String startMlgNum;

    /**
     * 止桩号
     */
    private String endMlgNum;

    /**
     * 灌溉面积
     */
    private Double irrArea;

    /**
     * 设计流量
     */
    private Double desFlow;

    /**
     * 水渠长度
     */
    private Double chanLen;

    /**
     * 设计水深
     */
    private Double desDepth;

    /**
     * 基础流量
     */
    private Double baseFlow;

    /**
     * 显示顺序
     */
    private Integer sort;
}