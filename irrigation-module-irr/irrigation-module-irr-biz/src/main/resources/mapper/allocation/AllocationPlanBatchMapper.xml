<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.allocation.AllocationPlanBatchMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="getCodeByYear" resultType="java.lang.Integer">
        SELECT `code` FROM irr_allocation_plan_batch WHERE year=#{year} ORDER BY `code` DESC limit 1
    </select>

    <select id="getSubmeterList" resultType="com.yutoudev.irrigation.irr.controller.admin.allocation.vo.planbatch.AllocationPlanBatchSubmeterVO">
        SELECT
            ap.id,
            wdi.chan_id,
            wdi.chan_name,
            ap.user_id,
            ap.user_name,
            ap.start_time,
            ap.end_time,
            ROUND( TIMESTAMPDIFF( SECOND, ap.start_time, ap.end_time ) / 3600, 2 ) AS duration,
            ap.flow_rate,
            ap.supply_water,
            wdi.gate_height,
            ROUND(wdi.gate_height * 100 / wdi.gate_max_height, 2 ) AS gate_height_rate,(
                SELECT
                    SUM( crop_area )
                FROM
                    irr_allocation_plan_item
                WHERE
                    plan_id = wdi.plan_id
            ) AS area,
            (SELECT CONCAT(nickname, ' - ', mobile) FROM system_users WHERE id=(SELECT user_id FROM irr_equip_manager WHERE equip_id = wdi.qu_equip_id AND user_id is not NULL LIMIT 1) ) as gate_link
        FROM
            `irr_water_diversion_inversion` wdi
            LEFT JOIN irr_allocation_plan ap ON ap.id = wdi.plan_id
        WHERE
            wdi.type = 1
          AND wdi.plan_id = ap.id
          AND ap.batch_id= #{id}
          AND wdi.is_discharge_gate = 0
          AND ap.chan_id = wdi.chan_id
        ORDER BY ap.start_time ASC
    </select>

    <select id="getSummaryList" resultType="com.yutoudev.irrigation.irr.controller.admin.allocation.vo.planbatch.AllocationPlanBatchSummaryVO">
        SELECT
            chan_id,
            chan_name,
            start_time,
            end_time,
            ROUND( TIMESTAMPDIFF( SECOND, start_time, end_time ) / 3600, 2 ) AS duration,
            loss flow_rate,
            gross supply_water

        FROM
            `irr_water_diversion_inversion`
        WHERE
            type = 1 AND is_discharge_gate = 0
            <if test="planIds != null">
                and plan_id REGEXP CONCAT('(^|,)(', #{planIds}, ')(,|$)')
            </if>
        ORDER BY
            chan_id,
            start_time ASC

    </select>

    <select id="getExecutingId" resultType="java.lang.Long">
        SELECT COALESCE(
           (SELECT batch.id
            FROM irr_allocation_plan_batch batch
                     LEFT JOIN irr_allocation_plan plan ON plan.batch_id = batch.id
            WHERE batch.`status` = 1
              AND plan.`status` = 2
            ORDER BY batch.`year`, batch.`code` ASC
           LIMIT 1),
          (SELECT id
           FROM irr_allocation_plan_batch
           WHERE `status` = 1
           ORDER BY id DESC
           LIMIT 1)
        ) AS final_id
    </select>
</mapper>
