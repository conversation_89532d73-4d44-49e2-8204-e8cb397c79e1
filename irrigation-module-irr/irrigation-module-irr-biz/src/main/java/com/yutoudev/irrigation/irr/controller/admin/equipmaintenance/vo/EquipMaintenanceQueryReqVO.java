package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.equipmaintenance.EquipMaintenanceDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 设备维护list查询RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护list查询RequestVO，参数和 EquipMaintenancePageReqVO 是一致的
 * @time 2024-10-27 10:33:13
 */
@Data
public class EquipMaintenanceQueryReqVO extends QueryCriteria<EquipMaintenanceDO> {

    /**
     * ID
     */
    private Long id;

    /**
     * 设备ID
     */
    private Long equipId;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 维护内容
     */
    private String description;

    /**
     * 维护结果
     */
    private String result;

    /**
     * 维护人员ID
     */
    private Long userId;

    /**
     * 维护日期
     */
    private LocalDateTime maintenanceDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 删除标志
     */
    private Boolean deleted;

    /**
     * 租户编号
     */
    private Long tenantId;

}
