package com.yutoudev.irrigation.irr.controller.admin.allocationplanmonth.vo;

import lombok.*;

import java.time.*;
  import java.math.BigDecimal;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yutoudev.irrigation.irr.controller.admin.userbase.vo.UserBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseRespVO;

/**
 *
 * 配水调度月计划ResponseVO
 * @description 管理后台-配水调度月计划ResponseVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:39
 *
 */
@Data
@ToString(callSuper = true)
public class AllocationPlanMonthRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 计划名称
     * 
     */
    private String name;

    /**
     * 用水用户ID
     * 
     */
    private Long userId;

    /**
     * 用水用户
     * 
     */
    private UserBaseRespVO user;
  
    /**
     * 灌溉片区
     * 
     */
    private ZoneBaseRespVO zone;
  
    /**
     * 灌溉片区ID
     * 
     */
    private Long zoneId;

    /**
     * 水渠
     * 
     */
    private ChanBaseRespVO chan;
  
    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水源
     * 
     */
    private SwhsBaseRespVO swhs;
  
    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 计划供水
     * @mock （m³）
     */
    private BigDecimal supplyWater;

    /**
     * 创建人
     * 
     */
    private String creator;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 计划时间
     * @mock （yyyy-MM）
     */
    private String planTime;

    /**
     * 修改时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;

    /**
     * 创建时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    /**
     * 修改人
     * 
     */
    private String updater;

    /**
     * 删除标志
     * 
     */
    private Boolean deleted;

    /**
     * 租户编号
     * 
     */
    private Long tenantId;
}