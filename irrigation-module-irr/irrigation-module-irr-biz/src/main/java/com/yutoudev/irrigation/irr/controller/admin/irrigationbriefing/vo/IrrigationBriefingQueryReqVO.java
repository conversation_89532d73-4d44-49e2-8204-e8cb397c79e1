package com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 灌区简报查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IrrigationBriefingQueryReqVO extends IrrigationBriefingBaseVO {

    @Schema(description = "简报日期")
    private LocalDateTime briefingDate;

    @Schema(description = "期数")
    private Integer issueNumber;

    @Schema(description = "编辑单位")
    private String editorUnit;

    @Schema(description = "报告数据范围开始时间")
    private LocalDateTime reportStartTime;

    @Schema(description = "报告数据范围结束时间")
    private LocalDateTime reportEndTime;
} 