<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.rainfallprocess.RainfallProcessBasinMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <insert id="saveOrUpdate" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO `irr_rainfall_process_basin` (
            `code`, `year`, `rainfall`, `start_time`, `end_time`,
            `large_time`, `small_time`, `runoff_arrival_time`, `runoff_time`,
            `fading_coef`, `swhs_id`, `swhs_name`, `chan_id`, `chan_name`
        ) VALUES (
             #{basinDO.code}, #{basinDO.year}, #{basinDO.rainfall}, #{basinDO.startTime}, #{basinDO.endTime},
             #{basinDO.largeTime}, #{basinDO.smallTime}, #{basinDO.runoffArrivalTime}, #{basinDO.runoffTime},
             #{basinDO.fadingCoef}, #{basinDO.swhsId}, #{basinDO.swhsName}, #{basinDO.chanId}, #{basinDO.chanName}
         )
        ON DUPLICATE KEY UPDATE
             `rainfall` = VALUES(`rainfall`),
             `start_time` = VALUES(`start_time`),
             `end_time` = VALUES(`end_time`),
             `large_time` = VALUES(`large_time`),
             `small_time` = VALUES(`small_time`),
             `runoff_arrival_time` = VALUES(`runoff_arrival_time`),
             `runoff_time` = VALUES(`runoff_time`),
             `fading_coef` = VALUES(`fading_coef`),
             `swhs_name` = VALUES(`swhs_name`),
             `chan_id` = VALUES(`chan_id`),
             `chan_name` = VALUES(`chan_name`)
    </insert>
</mapper>
