package com.yutoudev.irrigation.irr.controller.admin.waterdiversioninversion;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.waterdiversioninversion.vo.*;
import com.yutoudev.irrigation.irr.convert.waterdiversioninversion.WaterDiversionInversionConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.waterdiversioninversion.WaterDiversionInversionDO;
import com.yutoudev.irrigation.irr.service.waterdiversioninversion.WaterDiversionInversionService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 引水量反演
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/water-diversion-inversion")
@Validated
public class WaterDiversionInversionController {

    private static final String MODULE_NAME = "引水量反演";

    @Resource
    private WaterDiversionInversionService<WaterDiversionInversionDO> waterDiversionInversionService;

    /**
     * 创建引水量反演
     *
     * @param createReqVO WaterDiversionInversionCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody WaterDiversionInversionCreateReqVO createReqVO) {
        return success(waterDiversionInversionService.create(createReqVO));
    }

    /**
     * 批量创建引水量反演
     *
     * @param lists WaterDiversionInversionCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<WaterDiversionInversionCreateReqVO> lists) {
        return success(waterDiversionInversionService.createBatch(lists));
    }

    /**
     * 更新引水量反演
     *
     * @param updateReqVO WaterDiversionInversionUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody WaterDiversionInversionUpdateReqVO updateReqVO) {
        waterDiversionInversionService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新引水量反演
     *
     * @param lists 批量更新列表 WaterDiversionInversionUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<WaterDiversionInversionUpdateReqVO> lists) {
        return success(waterDiversionInversionService.updateBatch(lists));
    }

    /**
     * 删除引水量反演
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        waterDiversionInversionService.delete(id);
        return success(true);
    }

    /**
     * 批量删除引水量反演
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(waterDiversionInversionService.deleteBatch(ids));
    }

    /**
     * 获得引水量反演详情
     *
     * @param id 编号 Long
     * @return CommonResult<WaterDiversionInversionDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<WaterDiversionInversionDetailRespVO> get(@RequestParam("id") Long id) {
        WaterDiversionInversionDO waterDiversionInversion = waterDiversionInversionService.get(id);
        return success(WaterDiversionInversionConvert.INSTANCE.convertDetail(waterDiversionInversion));
    }

    /**
     * 引水量反演列表
     *
     * @param queryReqVO 查询条件 WaterDiversionInversionQueryReqVO
     * @return CommonResult<List < WaterDiversionInversionRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<WaterDiversionInversionRespVO>> getList(@RequestQueryParam WaterDiversionInversionQueryReqVO queryReqVO) {
        List<WaterDiversionInversionDO> list = waterDiversionInversionService.getList(queryReqVO);
        return success(WaterDiversionInversionConvert.INSTANCE.convertList(list));
    }

    /**
     * 引水量反演分页
     *
     * @param pageVO 查询条件 WaterDiversionInversionPageReqVO
     * @return CommonResult<PageResult < WaterDiversionInversionRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<WaterDiversionInversionRespVO>> page(@RequestQueryParam WaterDiversionInversionPageReqVO pageVO) {
        PageResult<WaterDiversionInversionDO> pageResult = waterDiversionInversionService.page(pageVO);
        return success(WaterDiversionInversionConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出引水量反演Excel
     *
     * @param queryReqVO 查询条件 WaterDiversionInversionExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam WaterDiversionInversionExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<WaterDiversionInversionDO> list = waterDiversionInversionService.getList(queryReqVO);
        // 导出 Excel
        List<WaterDiversionInversionExcelVO> datas = WaterDiversionInversionConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "引水量反演", "xlsx"), queryReqVO.getExportSheetName(),
                WaterDiversionInversionExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入引水量反演模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "引水量反演-导入模版.xls", "sheet1", WaterDiversionInversionExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入引水量反演Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:water-diversion-inversion:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<WaterDiversionInversionExcelVO> list = ExcelUtils.read(file, WaterDiversionInversionExcelVO.class);
        return success(waterDiversionInversionService.importExcel(list, isUpdate));
    }
}