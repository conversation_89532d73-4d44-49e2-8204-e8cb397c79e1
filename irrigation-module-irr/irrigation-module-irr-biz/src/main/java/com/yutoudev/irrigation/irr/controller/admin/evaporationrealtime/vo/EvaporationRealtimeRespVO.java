package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 实时蒸散发量ResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-实时蒸散发量ResponseVO
 * @time 2024-12-20 17:34:41
 */
@Data
@ToString(callSuper = true)
public class EvaporationRealtimeRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 时间
     */
    private LocalDateTime markTime;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 水面蒸发值
     */
    private Double evaporationValue;
}