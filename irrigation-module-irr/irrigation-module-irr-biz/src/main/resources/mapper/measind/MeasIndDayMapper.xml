<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndDayMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="sumWaterSupply" resultType="java.lang.Double">
        SELECT
            SUM( water_supply )
        FROM
            irr_meas_ind_day
        WHERE
            equip_id IN (
                SELECT
                    wge.equip_id
                FROM
                    irr_swhs_water_gate swg
                        LEFT JOIN irr_water_gate_equip wge ON swg.gate_id = wge.gate_id
                WHERE
                    swg.gate_type = 1
                  AND swg.swhs_id = #{swhsId}
            )
          AND `day` = #{day}
    </select>

    <select id="sumRainfallVolume" resultType="java.lang.Double">
        SELECT SUM(rainfall_volume) rainfall_volume FROM irr_meas_ind_day WHERE equip_id=#{equipId} AND `day` BETWEEN #{startTime} AND #{endTime}
    </select>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into irr_meas_ind_day(
            id,
            equip_id,
            central_id,
            dev_id,
            `day`,
            rainfall_volume,
            eight_water_level,
            twenty_water_level,
            max_water_level,
            min_water_level,
            avg_water_level,
            max_water_flow,
            min_water_flow,
            avg_water_flow,
            water_supply,
            water_volume,
            update_time
        )
        values
        <foreach collection="entities" item="entity" separator=",">
            (
             #{entity.id},
             #{entity.equipId},
             #{entity.centralId},
             #{entity.devId},
             #{entity.day},
             #{entity.rainfallVolume},
             #{entity.eightWaterLevel},
             #{entity.twentyWaterLevel},
             #{entity.maxWaterLevel},
             #{entity.minWaterLevel},
             #{entity.avgWaterLevel},
             #{entity.maxWaterFlow},
             #{entity.minWaterFlow},
             #{entity.avgWaterFlow},
             #{entity.waterSupply},
             #{entity.waterVolume},
             #{entity.updateTime})
        </foreach>
        on duplicate key update
        equip_id = values(equip_id),
        central_id = values(central_id),
        dev_id = values(dev_id),
        `day` = values(`day`),
        rainfall_volume = values(rainfall_volume),
        eight_water_level = values(eight_water_level),
        twenty_water_level = values(twenty_water_level),
        max_water_level = values(max_water_level),
        min_water_level = values(min_water_level),
        avg_water_level = values(avg_water_level),
        max_water_flow = values(max_water_flow),
        min_water_flow = values(min_water_flow),
        avg_water_flow = values(avg_water_flow),
        water_supply = values(water_supply),
        water_volume = values(water_volume),
        update_time = values(update_time)
    </insert>
</mapper>
