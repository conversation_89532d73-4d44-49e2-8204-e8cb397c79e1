package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute.vo;

import lombok.*;
import java.util.*;
import java.time.*;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.experimental.Accessors;

/**
 *
 * 来水分析明细执行记录ExcelVO
 * @description 管理后台-来水分析明细执行记录导出、导入ExcelVO
 * <AUTHOR>
 * @time 2024-08-09 10:49:53
 *
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class InletAnalysisDetailExecuteExcelVO {


    /**
     * ID
     * 
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 集水面积ID
     * 
     */
    @ExcelProperty("集水面积ID")
    private Long catchmentId;

    /**
     * 计算时间
     * 
     */
    @ExcelProperty("计算时间")
    private LocalDateTime computTime;

    /**
     * 执行数据
     * @mock json结构，存储设备及对应降雨量
     */
    @ExcelProperty("执行数据")
    private String executeData;

    /**
     * 蒸发量
     * 
     */
    @ExcelProperty("蒸发量")
    private Double evaporation;

    /**
     * 损耗值
     * 
     */
    @ExcelProperty("损耗值")
    private Double loss;

    /**
     * 径流值
     * 
     */
    @ExcelProperty("径流值")
    private Double runoff;

    /**
     * 径流时间
     * @mock 分钟
     */
    @ExcelProperty("径流时间")
    private Integer runoffTime;

    /**
     * 执行次数
     * 
     */
    @ExcelProperty("执行次数")
    private Integer executeNum;

    /**
     * 执行状态
     * @mock 0，待计算，1，计算完成
     */
    @ExcelProperty("执行状态")
    private Integer executeStatus;

    /**
     * 执行日志
     * 
     */
    @ExcelProperty("执行日志")
    private String executeLogs;

    /**
     * 创建时间
     * 
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
