package com.yutoudev.irrigation.irr.controller.admin.waterdiversioninversion.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 引水量反演Excel导出RequestVO
 *
 * <AUTHOR>
 */
@Data
@Builder
public class WaterDiversionInversionExportReqVO extends WaterDiversionInversionQueryReqVO {

    /**
     * 标题名称
     *
     * @description 若不特别说明，缺省为sheet1
     */
    private String exportSheetName = "sheet1";

    /**
     * 导出文件名
     *
     * @description 导出文件名称，若未设置则根据默认规则（实体中文名称+导出时间）产生文件名
     */
    private String exportFileName;

    /**
     * 导出指定列
     *
     * @description 字符串数组
     * @mock ['name','age']
     */
    private List<String> exportIncludeColumns;

    /**
     * 排除指定列
     *
     * @description 字符串数组
     * @mock ['createTime','age']
     */
    private List<String> exportExcludeColumns;

}
