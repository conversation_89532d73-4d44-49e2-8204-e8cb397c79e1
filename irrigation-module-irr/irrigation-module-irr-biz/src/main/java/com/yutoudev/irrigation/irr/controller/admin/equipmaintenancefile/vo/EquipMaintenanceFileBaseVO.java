package com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 设备维护附件 Base VO
 *
 * <AUTHOR>
 * @description 设备维护附件 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-10-27 10:33:14
 */
@Data
public class EquipMaintenanceFileBaseVO {

    /**
     * 设备维护ID
     */
    @NotNull(message = "设备维护ID不能为空")
    private Long maintenanceId;

    /**
     * 附件id
     */
    @NotNull(message = "附件id不能为空")
    private Long fileId;

    /**
     * 附件名称
     */
    @NotNull(message = "附件名称不能为空")
    private String fileName;

    /**
     * 附件url
     */
    @NotNull(message = "附件url不能为空")
    private String fileUrl;

    /**
     * 附件媒体类型
     */
    @NotNull(message = "附件媒体类型不能为空")
    private String fileType;
}
