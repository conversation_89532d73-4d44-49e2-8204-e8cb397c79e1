package com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 设备维护附件ExcelVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护附件导出、导入ExcelVO
 * @time 2024-10-27 10:33:14
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class EquipMaintenanceFileExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 设备维护ID
     */
    @ExcelProperty("设备维护ID")
    private Long maintenanceId;

    /**
     * 附件id
     */
    @ExcelProperty("附件id")
    private Long fileId;

    /**
     * 附件名称
     */
    @ExcelProperty("附件名称")
    private String fileName;

    /**
     * 附件url
     */
    @ExcelProperty("附件url")
    private String fileUrl;

    /**
     * 附件媒体类型
     */
    @ExcelProperty("附件媒体类型")
    private String fileType;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
