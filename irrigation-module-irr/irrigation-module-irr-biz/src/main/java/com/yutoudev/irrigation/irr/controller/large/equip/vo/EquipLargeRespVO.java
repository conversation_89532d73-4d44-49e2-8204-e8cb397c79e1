package com.yutoudev.irrigation.irr.controller.large.equip.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 站点管理大屏ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class EquipLargeRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 设备类型
     *
     * @mock 1量测设备，2闸控设备，3摄像头
     */
    private Integer groupType;

    /**
     * 测站代码
     */
    private String stCode;

    /**
     * 站点类型
     */
    private Integer stType;

    /**
     * 设备类型
     */
    private Integer devType;

    /**
     * 站点名称
     */
    private String stName;

    /**
     * 水源ID
     */
    private Long swhsId;

    /**
     * 设备GIS
     */
    private String stLocGis;

    /**
     * 设备位置
     */
    private String stLoc;

    /**
     * 桩号
     */
    private String dpdsNum;

    /**
     * 经度
     */
    private Double stLng;

    /**
     * 纬度
     */
    private Double stLat;

    /**
     * 海拔基值
     */
    private Double basicAltitude;

    /**
     * 建设年月
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date compDate;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;


    /**
     * 监控点唯一标识
     */
    private String cameraUid;

    /**
     * 视频通道
     */
    private Integer cameraChannel;

    /**
     * 摄像头类型 1枪机 2球机
     */
    private Integer cameraType;

    /**
     * 视频预览时长 (分钟)
     */
    private Integer previewTime;

    /**
     * 站点图片地址
     */
    private String picUrl;


    /**
     * 指令超时时间
     *
     * @apiNote （控制指令执行中每次上报数据时间间隔最大秒数）
     */
    private Integer instTimeout;

    /**
     * 在线状态
     *
     * @mock 0离线 1在线
     */
    private Integer online;

    /**
     * 拍照功能 0无，1有
     */
    private Integer takePhoto;

    /**
     * 通讯协议版本
     * v1：老设备,v2：新设备
     */
    private String protocol;

}