<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.waterdiversioninversion.WaterDiversionInversionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="queryListByGreaterThanTime" resultType="com.yutoudev.irrigation.irr.dal.dataobject.waterdiversioninversion.WaterDiversionInversionDO">
        SELECT
            wdi.*
        FROM
            irr_water_diversion_inversion wdi
                JOIN ( SELECT DISTINCT plan_id FROM irr_water_diversion_inversion WHERE type = 0 AND end_time > NOW() ) active_plans ON wdi.plan_id = active_plans.plan_id
        WHERE
            wdi.type = 0
        ORDER BY
            wdi.start_time ASC
    </select>

    <select id="querySwhsListByGreaterThanTime" resultType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelRespVo">
        SELECT * FROM `irr_water_diversion_inversion` WHERE type=1 AND (start_time > NOW() OR end_time > NOW()) AND swhs_id is not NULL
        <if test="planIds != null">
            and plan_id REGEXP CONCAT('(^|,)(', #{planIds}, ')(,|$)')
        </if>
        ORDER BY start_time ASC
    </select>

    <select id="queryChanListByGreaterThanTime" resultType="com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo.InletInversionModelRespVo">
        SELECT * FROM `irr_water_diversion_inversion` WHERE type=1 AND (start_time > NOW() OR end_time > NOW()) AND swhs_id is NULL
        <if test="planIds != null">
            and plan_id REGEXP CONCAT('(^|,)(', #{planIds}, ')(,|$)')
        </if>
        ORDER BY start_time ASC
    </select>

    <select id="queryListByPlanId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.waterdiversioninversion.WaterDiversionInversionDO">
        SELECT * FROM `irr_water_diversion_inversion` WHERE type=1  and plan_id REGEXP CONCAT('(^|,)(', #{planId}, ')(,|$)') ORDER BY start_time DESC
    </select>

    <select id="queryPlanIdByEndTimeGtNow" resultType="java.lang.String">
        SELECT plan_id FROM `irr_water_diversion_inversion` WHERE  end_time > NOW() GROUP BY plan_id
    </select>

    <delete id="deleteByPlanIds">
        delete FROM `irr_water_diversion_inversion` WHERE type=1 and plan_id  REGEXP CONCAT('(^|,)(', #{planIds}, ')(,|$)')
    </delete>

    <select id="queryEffectPlanIdsByPlanId" resultType="java.lang.String">
        SELECT plan_id FROM `irr_water_diversion_inversion` WHERE  type=1 and plan_id  REGEXP CONCAT('(^|,)(', #{planId}, ')(,|$)') GROUP BY plan_id
    </select>


</mapper>
