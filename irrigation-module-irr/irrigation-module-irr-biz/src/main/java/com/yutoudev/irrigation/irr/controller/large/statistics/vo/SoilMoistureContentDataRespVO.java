package com.yutoudev.irrigation.irr.controller.large.statistics.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 墒情数据统计ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-墒情数据统计ResponseVO
 * @time 2025-03-19
 */
@Data
@ToString(callSuper = true)
public class SoilMoistureContentDataRespVO {

    /**
     * 用水申请用户名称
     */
    private String userName;
    /**
     * 总灌溉面积
     */
    private Double area;

    /**
     * 受旱面积
     */
    private Double drylandArea;

    /**
     * 受旱面积占比，百分比值
     */
    private Double rate;

    /**
     * 土壤含水量
     */
    private Double soilWater;
}
