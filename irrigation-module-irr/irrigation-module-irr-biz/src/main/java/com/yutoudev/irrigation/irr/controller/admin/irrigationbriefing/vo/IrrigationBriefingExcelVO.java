package com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 灌区简报 Excel 导出 VO")
@Data
@ExcelIgnoreUnannotated
public class IrrigationBriefingExcelVO {

    @ExcelProperty("简报日期")
    private LocalDateTime briefingDate;

    @ExcelProperty("期数")
    private Integer issueNumber;

    @ExcelProperty("编辑单位")
    private String editorUnit;

    @ExcelProperty("报告数据范围开始时间")
    private LocalDateTime reportStartTime;

    @ExcelProperty("报告数据范围结束时间")
    private LocalDateTime reportEndTime;

    @ExcelProperty("报告内容")
    private String reportContent;

    @ExcelProperty("备注")
    private String remark;
} 