package com.yutoudev.irrigation.irr.controller.admin.equipculvertsluice.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 *
 * 站点涵闸配置Excel导出RequestVO
 * @description 管理后台-站点涵闸配置Excel导出RequestVO
 * <AUTHOR>
 * @time 2024-08-17 23:16:50
 *
 */
@Data
@Builder
public class EquipCulvertSluiceExportReqVO extends  EquipCulvertSluiceQueryReqVO {

		/**
		 * 标题名称
		 * @description 若不特别说明，缺省为sheet1
		 */
		private String exportSheetName = "sheet1";

		/**
		 * 导出文件名
		 * @description 导出文件名称，若未设置则根据默认规则（实体中文名称+导出时间）产生文件名
		 */
		private String exportFileName;

		/**
		 * 导出指定列
		 * @description 字符串数组
		 * @mock ['name','age']
		 */
		private List<String> exportIncludeColumns;

		/**
		 * 排除指定列
		 * @description 字符串数组
		 * @mock ['createTime','age']
		 */
		private List<String> exportExcludeColumns;

}
