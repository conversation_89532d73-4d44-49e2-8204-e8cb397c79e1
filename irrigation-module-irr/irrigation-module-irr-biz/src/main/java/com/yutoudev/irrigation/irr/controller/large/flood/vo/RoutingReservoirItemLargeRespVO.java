package com.yutoudev.irrigation.irr.controller.large.flood.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 水库调洪演算明细ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RoutingReservoirItemLargeRespVO {


    /**
     * ID
     */
    private Long id;


    /**
     * 时间
     */
    private LocalDateTime reportTime;

    /**
     * 入库流量
     */
    private Double inflow;

    /**
     * 水位
     */
    private Double waterLevel;

    /**
     * 泄流量
     */
    private Double dischargeFlow;

    /**
     * 库容量
     */
    private Double storageVolume;

    /**
     * 计算后库容量
     */
    private Double calcStorageVolume;

    /**
     * 计算后出库流量
     */
    private Double calcOutflow;

    /**
     * 计算后水位
     */
    private Double calcWaterLevel;

}