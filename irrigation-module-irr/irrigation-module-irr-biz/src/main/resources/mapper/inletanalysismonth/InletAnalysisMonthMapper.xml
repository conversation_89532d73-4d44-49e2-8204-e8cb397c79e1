<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.inletanalysismonth.InletAnalysisMonthMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="sumRainfallGroupBySwhsId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.inletanalysis.InletAnalysisDO">
        SELECT swhs_id,swhs_name,SUM(rainfall) rainfall,evaporation,penetration,`month` statistics_date FROM `irr_inlet_analysis_month` WHERE  chan_id is NULL AND `month`=#{month} GROUP BY swhs_id
    </select>


    <select id="sumRainfallGroupByChanId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.inletanalysis.InletAnalysisDO">
        SELECT chan_id,chan_name,SUM(rainfall) rainfall,evaporation,penetration,`month` statistics_date FROM `irr_inlet_analysis_month` WHERE chan_id is NOT NULL AND `month`=#{month} GROUP BY chan_id
    </select>

    <select id="sumRainfallGroupByCatchmentIdAndYear" resultType="java.lang.Double">
        SELECT SUM(rainfall) from irr_inlet_analysis_month WHERE catchment_id=#{catchmentId} AND `month` LIKE CONCAT(#{year},'%')
    </select>

    <select id="sumRainfallVolumeGroupByCatchmentIdAndYear" resultType="java.lang.Double">
        SELECT SUM(rainfall_volume) from irr_inlet_analysis_month WHERE catchment_id=#{catchmentId} AND `month` LIKE CONCAT(#{year},'%')
    </select>
</mapper>
