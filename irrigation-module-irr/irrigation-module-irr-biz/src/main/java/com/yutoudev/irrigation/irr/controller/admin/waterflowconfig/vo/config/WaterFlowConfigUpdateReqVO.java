package com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.config;

import lombok.*;

import javax.validation.constraints.*;


/**
 *
 * 水流配置更新RequestVO
 * @description 管理后台-水流配置更新RequestVO
 * <AUTHOR>
 * @time 2024-08-13 15:15:33
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WaterFlowConfigUpdateReqVO extends WaterFlowConfigBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}