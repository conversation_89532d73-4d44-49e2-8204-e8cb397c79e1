package com.yutoudev.irrigation.irr.controller.admin.allocationplanestimate.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;

/**
 *
 * 配水调度计划估算 Base VO
 * @description 配水调度计划估算 Base VO，提供给添加、修改、详细的子 VO 使用
 * <AUTHOR>
 * @time 2024-09-16 11:16:31
 *
 */
@Data
public class AllocationPlanEstimateBaseVO {

    /**
     * 配水计划ID
     * 
     */
    private Long planId;

    /**
     * 类型
     * @mock ：0，年；1，月；2，日
     */
    private Integer type;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水源名称
     * 
     */
    private String swhsName;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水渠名称
     * 
     */
    private String chanName;

    /**
     * 子级水渠ID
     * 
     */
    private Long childChanId;

    /**
     * 对应当前水渠或水源出水口ID
     * 
     */
    private Long gateId;

    /**
     * 出水口名称
     * 
     */
    private String gateName;

    /**
     * 计划供水
     * @mock （m³）
     */
    private Double supplyWater;

    /**
     * 计划供水开始时间
     * 
     */
    private LocalDateTime startTime;

    /**
     * 计划供水结束时间
     * 
     */
    private LocalDateTime endTime;

    /**
     * 损耗值
     * 
     */
    private Double loss;

    /**
     * 径流时间
     * @mock （分钟）
     */
    private Long runoffTime;
}
