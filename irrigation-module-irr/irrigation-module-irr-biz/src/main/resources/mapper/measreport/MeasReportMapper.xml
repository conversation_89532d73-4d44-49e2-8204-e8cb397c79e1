<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measreport.MeasReportMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->
    <select id="checkTableExists" resultType="java.lang.Boolean">
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'irr_meas_report_${shardingKey}'
        )
    </select>

    <select id="getMaxId" resultType="java.lang.Long">
        SELECT MAX(id) max_id FROM irr_meas_report_${shardingKey}
    </select>
    
    <update id="createShardingTable">
        CREATE TABLE IF NOT EXISTS irr_meas_report_${shardingKey} LIKE irr_meas_report
    </update>
    
    <update id="setTableAutoIncrement">
        ALTER TABLE irr_meas_report_${shardingKey} AUTO_INCREMENT = #{autoIncrement}
    </update>

</mapper>
