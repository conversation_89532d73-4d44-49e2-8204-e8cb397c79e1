package com.yutoudev.irrigation.irr.controller.large.statistics.vo;

import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 雨情统计ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-雨情统计ResponseVO
 * @time 2025-03-11
 */
@Data
@ToString(callSuper = true)
public class RainConditionRespVO {

    /**
     * 日累计降雨量（均值） 降雨量单位均为mm
     */
    private Double dayRainfall;

    /**
     * 最大降雨量
     */
    private Double maxDayRainfall;

    /**
     * 最大降雨量站点名称
     */
    private String maxDayRainfallEquipName;

    /**
     * 月累计降雨量
     */
    private Double monthRainfall;

    /**
     * 月累计环比
     */
    private Double monthQOQ;

    /**
     * 月累计同比
     */
    private Double monthYOY;

    /**
     * 年累计降雨量
     */
    private Double yearRainfall;

    /**
     * 月累计环比
     */
    private Double yearQOQ;

    /**
     * 月累计同比
     */
    private Double yearYOY;


}
