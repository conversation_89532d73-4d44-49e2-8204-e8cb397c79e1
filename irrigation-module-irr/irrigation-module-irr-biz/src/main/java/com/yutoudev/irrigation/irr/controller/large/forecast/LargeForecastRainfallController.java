package com.yutoudev.irrigation.irr.controller.large.forecast;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.common.util.date.DateUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.forecast.vo.rainfall.ForecastRainfallPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.forecast.vo.rainfall.ForecastRainfallRespVO;
import com.yutoudev.irrigation.irr.convert.forecast.ForecastRainfallConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.forecast.ForecastRainfallDO;
import com.yutoudev.irrigation.irr.service.forecast.ForecastRainfallService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 降雨径流量
 *
 * <AUTHOR>
 * @description 管理后台-降雨径流量controller
 * @time 2024-12-01 23:09:04
 */
@RestController
@RequestMapping("/irr/forecast-rainfall")
@Validated
public class LargeForecastRainfallController {

    private static final String MODULE_NAME = "降雨径流量";

    @Resource
    private ForecastRainfallService<ForecastRainfallDO> forecastRainfallService;

    /**
     * 未来10天降雨径流量列表
     *
     * @return CommonResult<List < ForecastRainfallRespVO>> 列表响应VO
     */
    @GetMapping("/day10")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<ForecastRainfallRespVO>> getListByDay10() {
        List<ForecastRainfallDO> list = forecastRainfallService.getListByDay10();
        Map<String, List<ForecastRainfallDO>> groupedList = list.stream()
                .collect(Collectors.groupingBy(
                        forecastRainfallDO -> forecastRainfallDO.getForecastDay().format(DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY))
                ));
        // 如果10天不完整的要补全
        for (int i = 1; i <= 11; i++) {
            LocalDate tempDate = LocalDate.now().plusDays(i);
            String key = tempDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY));
            if (!groupedList.containsKey(key)) {
                ForecastRainfallDO forecastRainfallDO = new ForecastRainfallDO();
                forecastRainfallDO.setForecastDay(tempDate);
                forecastRainfallDO.setRainfall(0.0);
                forecastRainfallDO.setInflow(0.0);
                forecastRainfallDO.setRunoffCoef(0.0);
                list.add(forecastRainfallDO);
            }
        }
        list.sort(Comparator.comparing(ForecastRainfallDO::getForecastDay));
        return success(ForecastRainfallConvert.INSTANCE.convertList(list));
    }


    /**
     * 降雨径流量分页
     *
     * @param pageVO 查询条件 ForecastRainfallPageReqVO
     * @return CommonResult<PageResult < ForecastRainfallRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<ForecastRainfallRespVO>> page(@RequestQueryParam ForecastRainfallPageReqVO pageVO) {
        PageResult<ForecastRainfallDO> pageResult = forecastRainfallService.page(pageVO);
        return success(ForecastRainfallConvert.INSTANCE.convertPage(pageResult));
    }

}