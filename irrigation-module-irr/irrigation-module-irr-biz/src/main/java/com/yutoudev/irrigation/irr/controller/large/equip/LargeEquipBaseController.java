package com.yutoudev.irrigation.irr.controller.large.equip;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanEquipDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.*;
import com.yutoudev.irrigation.irr.controller.large.equip.vo.EquipLargeRespVO;
import com.yutoudev.irrigation.irr.controller.large.equip.vo.EquipOnlineStatsRespVO;
import com.yutoudev.irrigation.irr.convert.equipbase.EquipBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.util.dto.CameraBlowUpDto;
import com.yutoudev.irrigation.irr.util.dto.CameraOperatorDto;
import com.yutoudev.irrigation.irr.util.dto.PlayQueryDto;
import com.yutoudev.irrigation.irr.util.vo.VideoResultVo;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;


/**
 * 站点管理
 *
 * <AUTHOR>
 * @description 大屏-站点管理controller
 * @time 2024-05-11 15:41:24
 */
@RestController
@RequestMapping("/irr/equip-base")
@Validated
public class LargeEquipBaseController {

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    /**
     * 获得站点管理详情
     *
     * @param id 编号 Long
     * @return CommonResult<EquipBaseDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    public CommonResult<EquipBaseDetailRespVO> get(@RequestParam("id") Long id) {
        EquipBaseDO EquipBase = equipBaseService.get(id);
        return success(EquipBaseConvert.INSTANCE.convertDetail(EquipBase));
    }

    /**
     * 站点管理列表
     *
     * @param queryReqVO 查询条件 EquipBaseQueryReqVO
     * @return CommonResult<List < EquipBaseRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    public CommonResult<List<EquipLargeRespVO>> getList(@RequestQueryParam EquipBaseQueryReqVO queryReqVO) {
        List<EquipBaseDO> list = equipBaseService.getList(queryReqVO);
        return success(EquipBaseConvert.INSTANCE.convertListToLarge(list));
    }


    /**
     * 站点管理精简对象列表
     *
     * @param queryReqVO 查询条件 EquipBaseQueryReqVO
     * @return CommonResult<List < EquipBaseRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list-simple")
    public CommonResult<List<EquipBaseSimpleRespVO>> getListSimple(@RequestQueryParam EquipBaseQueryReqVO queryReqVO) {
        List<EquipBaseDO> list = equipBaseService.getList(queryReqVO);
        return success(EquipBaseConvert.INSTANCE.convertSimpleList(list));
    }

    /**
     * 站点管理关联的摄像头列表
     *
     * @param id 编号 Long
     * @return CommonResult<List < EquipBaseRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/listChan")
    public CommonResult<List<ChanEquipDetailRespVO>> listChan(@RequestParam("id") Long id) {
        List<ChanEquipDetailRespVO> list = equipBaseService.listChan(id);
        return success(list);
    }

    /**
     * 闸控关联摄像头列表
     *
     * @param queryReqVO 查询条件 EquipBaseQueryReqVO
     * @return CommonResult<List < EquipLargeRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/gate-camera-list")
    public CommonResult<List<EquipLargeRespVO>> gateCameraList(@RequestQueryParam EquipGateCameraQueryReqVO queryReqVO) {
        List<EquipBaseDO> list = equipBaseService.getGateCameraList(queryReqVO);
        return success(EquipBaseConvert.INSTANCE.convertListToLarge(list));
    }

    /**
     * 返回配水管理未执行或未完成执行的站点摄像头列表
     *
     * @return CommonResult<List < EquipLargeRespVO>> 列表响应VO
     * @description 闸控过程实时视频：在配水计划中正在执行待执行，站点关联的摄像头
     */
    @GetMapping("/gate-camera-list/allocation-water")
    public CommonResult<List<EquipLargeRespVO>> getGateCameraByAllocationWaterList() {
        List<EquipBaseDO> list = equipBaseService.getGateCameraByAllocationWaterList();
        return success(EquipBaseConvert.INSTANCE.convertListToLarge(list));
    }

    /**
     * 站点管理分页
     *
     * @param pageVO 查询条件 EquipBasePageReqVO
     * @return CommonResult<PageResult < EquipBaseRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    public CommonResult<PageResult<EquipLargeRespVO>> page(@RequestQueryParam EquipBasePageReqVO pageVO) {
        PageResult<EquipBaseDO> pageResult = equipBaseService.page(pageVO);
        return success(EquipBaseConvert.INSTANCE.convertPageToLarge(pageResult));
    }

    /**
     * 返回摄像头在线状态
     *
     * @param dto PlayQueryDto
     * @return CommonResult<Integer>
     **/
    @PostMapping("/cameraStatus")
    public CommonResult<Integer> cameraStatus(@RequestBody PlayQueryDto dto) {
        return success(equipBaseService.getCameraStatus(dto.getApiType(), dto.getCode(), dto.getChannelNo()));
    }

    /**
     * 根据摄像头编码获取摄像头播放视频流
     *
     * @param dto
     * @return CommonResult<VideoResultVo>
     * @author: zhenglong
     * @date: 2024/6/6 10:58
     **/
    @PostMapping("/getVideoUrl")
    public CommonResult<VideoResultVo> getVideoUrl(@RequestBody PlayQueryDto dto) {
        return success(equipBaseService.getVideoUrl(dto));
    }

    /**
     * 根据摄像头编码获取摄像头回放视频流
     *
     * @param dto
     * @return CommonResult<VideoResultVo>
     **/
    @PostMapping("/getVideoReplayUrl")
    public CommonResult<VideoResultVo> getVideoReplayUrl(@RequestBody PlayQueryDto dto) {
        return success(equipBaseService.getVideoReplayUrl(dto));
    }

    /**
     * 根据摄像头编码操作摄像头旋转
     **/
    @PostMapping("/operator")
    public CommonResult<Boolean> operatorCamera(@RequestBody CameraOperatorDto dto) {
        equipBaseService.operatorCamera(dto);
        return success(true);
    }


    /**
     * 根据摄像头编码操作摄像头进行放大
     *
     * @author: zhenglong
     * @date: 2024/6/6 10:58
     * @param: [dto]
     * @return: com.yutoudev.irrigation.framework.common.pojo.CommonResult<java.lang.Boolean>
     **/
    @PostMapping("/blowUp")
    public CommonResult<Boolean> operatorCameraBlowUp(@RequestBody CameraBlowUpDto dto) {
        equipBaseService.operatorCameraBlowUp(dto);
        return success(true);
    }


    /**
     * 获取设备统计信息
     *
     * @return CommonResult<List < EquipStatisticsRespVO>> 设备统计信息列表
     */
    @GetMapping("/stats-online")
    public CommonResult<List<EquipOnlineStatsRespVO>> statsOnline() {
        return success(equipBaseService.statsOnline());
    }
}