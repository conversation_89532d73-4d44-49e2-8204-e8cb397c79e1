package com.yutoudev.irrigation.irr.controller.large.statistics.vo;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 用水信息ResponseVO
 *
 * <AUTHOR>
 * @description 大屏接口-用水信息ResponseVO
 * @time 2025-03-12
 */
@Data
@ToString(callSuper = true)
public class UseWaterInfoRespVO {

    /**
     * 当前水量 万m³
     */
    private Double current;

    /**
     * 累计用水量 万m³
     */
    private Double accumulate;

    /**
     * 用水分析
     */
    private List<UseTypeWaterVO> useTypeWaters;


}
