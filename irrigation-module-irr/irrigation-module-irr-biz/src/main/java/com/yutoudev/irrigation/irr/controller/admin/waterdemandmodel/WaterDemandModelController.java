package com.yutoudev.irrigation.irr.controller.admin.waterdemandmodel;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.waterdemandmodel.vo.WaterDemandModelByDayRespVo;
import com.yutoudev.irrigation.irr.controller.admin.waterdemandmodel.vo.WaterDemandModelQueryReqVo;
import com.yutoudev.irrigation.irr.controller.admin.waterdemandmodel.vo.WaterDemandModelRespVo;
import com.yutoudev.irrigation.irr.service.waterdemandmodel.WaterDemandModelService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 用水需求模型
 *
 * <AUTHOR>
 * @description 管理后台-用水需求模型controller
 * @time 2024-06-24 21:20:01
 */
@RestController
@RequestMapping("/irr/water-demand-model")
@Validated
public class WaterDemandModelController {

    private static final String MODULE_NAME = "用水需求模型";

    @Resource
    private WaterDemandModelService waterDemandModelService;

    /**
     * 用水需求模型-统计数据
     * @param queryReqVO
     * @return
     */
    @GetMapping("/statistics")
    @PreAuthorize("@ss.hasPermission('irr:water-demand-model:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<WaterDemandModelRespVo> getStatistics(@RequestQueryParam WaterDemandModelQueryReqVo queryReqVO) {
        WaterDemandModelRespVo respVo = waterDemandModelService.getStatistics(queryReqVO);
        return success(respVo);
    }

    /**
     * 用水需求模型-日统计柱状数据
     * @param queryReqVO
     * @return
     */
    @GetMapping("/day")
    @PreAuthorize("@ss.hasPermission('irr:water-demand-model:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<WaterDemandModelByDayRespVo>> getDayInfo(@RequestQueryParam WaterDemandModelQueryReqVo queryReqVO) {
        List<WaterDemandModelByDayRespVo> respVo = waterDemandModelService.getDay(queryReqVO);
        return success(respVo);
    }

}