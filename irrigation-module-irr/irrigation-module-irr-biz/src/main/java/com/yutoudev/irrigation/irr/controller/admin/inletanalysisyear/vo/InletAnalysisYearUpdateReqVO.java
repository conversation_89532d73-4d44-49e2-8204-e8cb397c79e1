package com.yutoudev.irrigation.irr.controller.admin.inletanalysisyear.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 年来水分析更新RequestVO
 * @description 管理后台-年来水分析更新RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:39
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisYearUpdateReqVO extends InletAnalysisYearBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}