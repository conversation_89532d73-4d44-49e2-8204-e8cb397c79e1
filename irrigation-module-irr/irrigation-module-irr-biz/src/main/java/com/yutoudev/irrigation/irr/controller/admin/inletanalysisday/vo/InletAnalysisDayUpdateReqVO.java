package com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 日来水分析更新RequestVO
 * @description 管理后台-日来水分析更新RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:36
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisDayUpdateReqVO extends InletAnalysisDayBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}