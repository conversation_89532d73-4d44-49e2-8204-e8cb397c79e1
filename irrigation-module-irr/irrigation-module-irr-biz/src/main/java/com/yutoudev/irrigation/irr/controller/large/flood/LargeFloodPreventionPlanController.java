package com.yutoudev.irrigation.irr.controller.large.flood;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.floodplan.vo.FloodPreventionPlanPageReqVO;
import com.yutoudev.irrigation.irr.controller.large.flood.vo.FloodPreventionPlanLargeRespVO;
import com.yutoudev.irrigation.irr.convert.floodplan.FloodPreventionPlanConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.floodplan.FloodPreventionPlanDO;
import com.yutoudev.irrigation.irr.enums.DictTypeConstants;
import com.yutoudev.irrigation.irr.service.floodplan.FloodPreventionPlanService;
import com.yutoudev.irrigation.module.system.api.dict.DictDataApi;
import com.yutoudev.irrigation.module.system.api.dict.dto.DictDataRespDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

/**
 * 大屏接口 - 防洪预案
 */
@RestController
@RequestMapping("/irr/flood-prevention-plan")
@Validated
public class LargeFloodPreventionPlanController {

    @Resource
    private FloodPreventionPlanService floodPreventionPlanService;

    @Resource
    private DictDataApi dictDataApi;

    /**
     * 获得防洪预案分页
     *
     * @param pageVO
     * @return
     */
    @GetMapping("/page")
    public CommonResult<PageResult<FloodPreventionPlanLargeRespVO>> getFloodPreventionPlanPage(@Valid FloodPreventionPlanPageReqVO pageVO) {
        PageResult<FloodPreventionPlanDO> pageResult = floodPreventionPlanService.getFloodPreventionPlanPage(pageVO);
        PageResult<FloodPreventionPlanLargeRespVO> result = FloodPreventionPlanConvert.INSTANCE.convertLargePage(pageResult);
        for (FloodPreventionPlanLargeRespVO resp : result.getList()) {
            if (Objects.nonNull(resp.getPlanLevel())) {
                DictDataRespDTO dict = dictDataApi.getDictData(DictTypeConstants.IRR_DROUGHT_LEVEL, resp.getPlanLevel().toString());
                if (Objects.nonNull(dict)) {
                    resp.setPlanLevelLabel(dict.getLabel());
                }
            }

            if (Objects.nonNull(resp.getPlanStatus())) {
                DictDataRespDTO dict = dictDataApi.getDictData(DictTypeConstants.FLOOD_PLAN_STATUS, resp.getPlanStatus().toString());
                resp.setPlanStatusLabel(dict.getLabel());
            }
        }
        return success(result);
    }
} 