package com.yutoudev.irrigation.irr.controller.admin.irrigationbriefing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 灌区简报导出 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IrrigationBriefingExportReqVO extends IrrigationBriefingBaseVO {

    @Schema(description = "简报日期")
    private LocalDateTime briefingDate;

    @Schema(description = "期数")
    private Integer issueNumber;

    @Schema(description = "编辑单位")
    private String editorUnit;

    @Schema(description = "报告数据范围开始时间")
    private LocalDateTime reportStartTime;

    @Schema(description = "报告数据范围结束时间")
    private LocalDateTime reportEndTime;

    @Schema(description = "导出文件名")
    private String exportFileName;

    @Schema(description = "导出sheet名称")
    private String exportSheetName;

    @Schema(description = "导出列名")
    private List<String> exportIncludeColumns;

    @Schema(description = "排除导出列名")
    private List<String> exportExcludeColumns;
} 