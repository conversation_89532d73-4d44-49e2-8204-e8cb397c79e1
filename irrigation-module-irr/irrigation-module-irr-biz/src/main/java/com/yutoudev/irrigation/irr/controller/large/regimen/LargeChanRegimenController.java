package com.yutoudev.irrigation.irr.controller.large.regimen;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.large.regimen.vo.ChanRegimenRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.chanbase.ChanBaseDO;
import com.yutoudev.irrigation.irr.service.chanbase.ChanBaseService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/chan")
@Validated
public class LargeChanRegimenController {

    private static final String MODULE_NAME = "大屏-渠道水情";

    @Resource
    private ChanBaseService<ChanBaseDO> chanBaseService;

    @GetMapping("/page-regimen")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<ChanRegimenRespVO>> pageRegimen(@RequestQueryParam ChanBasePageReqVO pageReqVO) {
        return success(chanBaseService.pageRegimen(pageReqVO));
    }
}
