package com.yutoudev.irrigation.irr.controller.large.flood.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 水库调洪演算ResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RoutingReservoirLargeRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 次数
     */
    private Integer code;

    /**
     * 年度
     */
    private Integer year;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 调洪演算标签
     */
    private String routingLabel;

    /**
     * 下游允许最大下泄流量
     */
    private Double maxAllowedDischarge;

    /**
     * 起调水位
     */
    private Double initWaterLevel;

    /**
     * 汛期防洪限制水位(m)
     */
    private Double controlLevel;

    /**
     * 控制下泄流量
     */
    private Double controlDischarge;

    /**
     * 计算单位
     */
    private Integer volumeUnit;

    /**
     * 最大下泄流量
     */
    private Double maxOutFlow;

    /**
     * 最大下泄时间
     */
    private LocalDateTime maxOutFlowTime;

    /**
     * 最高洪水位
     */
    private Double maxWaterLevel;

    /**
     * 最高洪水时间
     */
    private LocalDateTime maxWaterLevelTime;

    /**
     * 最大库容
     */
    private Double maxStorage;

    /**
     * 最大库容时间
     */
    private LocalDateTime maxStorageTime;

    /**
     * 演算明细
     */
    private List<RoutingReservoirItemLargeRespVO> items;
}