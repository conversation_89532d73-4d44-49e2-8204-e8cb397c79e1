<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.map.MapLayerMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectEquipListByLayerId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO">
        SELECT ieb.*
        FROM irr_equip_base AS ieb
                 INNER JOIN irr_map_layer_equip AS mle ON mle.equip_id = ieb.id
        WHERE mle.layer_id = #{layerId}
    </select>
</mapper>
