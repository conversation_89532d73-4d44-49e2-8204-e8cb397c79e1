package com.yutoudev.irrigation.irr.controller.large.building.vo;

import lombok.Data;

/**
 * 管理后台 - 工情类型统计 Response VO
 *
 * <AUTHOR>
 */
@Data
public class BuildingSituationTypeCountRespVO {
    
    /**
     * 工情类型
     */
    private Integer type;
    
    /**
     * 建筑物名称
     */
    private String name;

    /**
     * 建筑物总量
     */
    private Integer total;

    /**
     * 正常数量
     */
    private Integer normalCount;
    
    /**
     * 异常数量
     */
    private Integer errorCount;
}