package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.constraints.*;
import javax.validation.*;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;


import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisdetailexecute.InletAnalysisDetailExecuteDO;
import com.yutoudev.irrigation.irr.convert.inletanalysisdetailexecute.InletAnalysisDetailExecuteConvert;
import com.yutoudev.irrigation.irr.service.inletanalysisdetailexecute.InletAnalysisDetailExecuteService;


/**
 *
 * 来水分析明细执行记录
 * @description 管理后台-来水分析明细执行记录controller
 * <AUTHOR>
 * @time 2024-08-09 10:49:53
 *
 */
@RestController
@RequestMapping("/irr/inlet-analysis-detail-execute")
@Validated
public class InletAnalysisDetailExecuteController {

    private static final String MODULE_NAME = "来水分析明细执行记录";

    @Resource
    private InletAnalysisDetailExecuteService<InletAnalysisDetailExecuteDO> inletAnalysisDetailExecuteService;

    /**
     * 创建来水分析明细执行记录
     * @description 单个对象保存
     * @param createReqVO InletAnalysisDetailExecuteCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody InletAnalysisDetailExecuteCreateReqVO createReqVO) {
        return success(inletAnalysisDetailExecuteService.create(createReqVO));
    }

    /**
     * 批量创建来水分析明细执行记录
     * @description 多个对象保存
     * @param lists  InletAnalysisDetailExecuteCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<InletAnalysisDetailExecuteCreateReqVO> lists) {
        return success(inletAnalysisDetailExecuteService.createBatch(lists));
    }

    /**
     * 更新来水分析明细执行记录
     * @description 单个对象修改
     * @param updateReqVO InletAnalysisDetailExecuteUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody InletAnalysisDetailExecuteUpdateReqVO updateReqVO) {
        inletAnalysisDetailExecuteService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新来水分析明细执行记录
     * @description 批量更新
     * @param lists 批量更新列表 InletAnalysisDetailExecuteUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<InletAnalysisDetailExecuteUpdateReqVO> lists) {
        return success(inletAnalysisDetailExecuteService.updateBatch(lists));
    }

    /**
     * 删除来水分析明细执行记录
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        inletAnalysisDetailExecuteService.delete(id);
        return success(true);
    }

    /**
     * 批量删除来水分析明细执行记录
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(inletAnalysisDetailExecuteService.deleteBatch(ids));
    }

    /**
     * 获得来水分析明细执行记录详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<InletAnalysisDetailExecuteDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<InletAnalysisDetailExecuteDetailRespVO> get(@RequestParam("id") Long id) {
        InletAnalysisDetailExecuteDO inletAnalysisDetailExecute = inletAnalysisDetailExecuteService.get(id);
        return success(InletAnalysisDetailExecuteConvert.INSTANCE.convertDetail(inletAnalysisDetailExecute));
    }

    /**
     * 来水分析明细执行记录列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 InletAnalysisDetailExecuteQueryReqVO
     * @return CommonResult<List<InletAnalysisDetailExecuteRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<InletAnalysisDetailExecuteRespVO>> getList(@RequestQueryParam InletAnalysisDetailExecuteQueryReqVO queryReqVO) {
        List<InletAnalysisDetailExecuteDO> list = inletAnalysisDetailExecuteService.getList(queryReqVO);
        return success(InletAnalysisDetailExecuteConvert.INSTANCE.convertList(list));
    }

    /**
     * 来水分析明细执行记录分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 InletAnalysisDetailExecutePageReqVO
     * @return CommonResult<PageResult<InletAnalysisDetailExecuteRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<InletAnalysisDetailExecuteRespVO>> page(@RequestQueryParam InletAnalysisDetailExecutePageReqVO pageVO) {
        PageResult<InletAnalysisDetailExecuteDO> pageResult = inletAnalysisDetailExecuteService.page(pageVO);
        return success(InletAnalysisDetailExecuteConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出来水分析明细执行记录Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 InletAnalysisDetailExecuteExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam InletAnalysisDetailExecuteExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<InletAnalysisDetailExecuteDO> list = inletAnalysisDetailExecuteService.getList(queryReqVO);
        // 导出 Excel
        List<InletAnalysisDetailExecuteExcelVO> datas = InletAnalysisDetailExecuteConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "来水分析明细执行记录", "xlsx"), queryReqVO.getExportSheetName(),
                                                    InletAnalysisDetailExecuteExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入来水分析明细执行记录模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "来水分析明细执行记录-导入模版.xls", "sheet1", InletAnalysisDetailExecuteExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入来水分析明细执行记录Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail-execute:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<InletAnalysisDetailExecuteExcelVO> list = ExcelUtils.read(file, InletAnalysisDetailExecuteExcelVO.class);
        return success(inletAnalysisDetailExecuteService.importExcel(list, isUpdate));
    }
}