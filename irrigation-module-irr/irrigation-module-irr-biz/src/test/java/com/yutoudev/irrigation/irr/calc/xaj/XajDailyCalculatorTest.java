package com.yutoudev.irrigation.irr.calc.xaj;

import com.yutoudev.irrigation.irr.calc.unithydrograph.dto.UnitHydrographData;
import com.yutoudev.irrigation.irr.calc.xaj.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 新安江日模型测试类
 * 
 * <AUTHOR>
 */
class XajDailyCalculatorTest {

    private XajDailyCalculator calculator;
    private XajParameters params;
    private List<UnitHydrographData> unitHydrographList;

    @BeforeEach
    void setUp() {
        // 初始化模型参数
        params = new XajParameters();
        params.setArea(1000.0); // 流域面积 1000 km²
        params.setWM(150.0);    // 流域蓄水容量
        params.setWUM(20.0);    // 上层蓄水容量
        params.setWLM(60.0);    // 下层蓄水容量
        params.setB(0.3);       // 蓄水容量曲线指数
        params.setIM(0.01);     // 不透水面积比例
        params.setK(0.9);       // 蒸散发折算系数
        params.setC(0.15);      // 深层蒸散发系数
        params.setSM(20.0);     // 自由水蓄水容量
        params.setEX(1.5);      // 自由水蓄水容量指数
        params.setKI(0.3);      // 壤中流出流系数
        params.setKG(0.2);      // 地下水出流系数
        params.setCS(0.5);      // 地面径流消退系数
        params.setCI(0.7);      // 壤中流消退系数
        params.setCG(0.98);     // 地下径流消退系数

        // 初始状态变量
        params.setWU0(15.0);    // 初始上层土壤含水量
        params.setWL0(45.0);    // 初始下层土壤含水量
        params.setWD0(50.0);    // 初始深层土壤含水量

        // 初始化日单位线数据（示例：5天单位线）
        unitHydrographList = new ArrayList<>();
//        unitHydrographList.add(new UnitHydrographData(1, 0.4));
//        unitHydrographList.add(new UnitHydrographData(2, 0.3));
//        unitHydrographList.add(new UnitHydrographData(3, 0.2));
//        unitHydrographList.add(new UnitHydrographData(4, 0.08));
//        unitHydrographList.add(new UnitHydrographData(5, 0.02));

        calculator = new XajDailyCalculator(params, null, unitHydrographList);
    }

    @Test
    void testCalculatorInitialization() {
        assertNotNull(calculator);
        assertEquals(params, calculator.getParams());
        assertEquals(unitHydrographList, calculator.getDailyUnitHydrographList());
    }

    @Test
    void testSingleDayCalculation() {
        // 创建单日计算数据
        XajDailyCalcData calcData = new XajDailyCalcData();
        calcData.setReportDate(LocalDate.of(2024, 6, 1));
        calcData.setDailyPrecipitation(30.0); // 30mm降雨
        calcData.setDailyEvaporation(5.0);    // 5mm蒸发
        calcData.setF(1000.0); // 流域面积

        // 执行计算
        calculator.calculate(calcData, null);

        // 验证计算结果
        assertTrue(calcData.getDailyR() >= 0, "日总径流量应大于等于0");
        assertTrue(calcData.getDailyPE() >= 0, "日净雨量应大于等于0");
        assertTrue(calcData.getW() > 0, "总土壤含水量应大于0");
        assertTrue(calcData.getDailyQ() >= 0, "日出口流量应大于等于0");

        // 验证水量平衡
        double waterBalance = calcData.getWU() + calcData.getWL() + calcData.getWD();
        assertEquals(calcData.getW(), waterBalance, 0.001, "土壤含水量计算错误");

        System.out.println("单日计算结果：");
        System.out.println("日降雨量: " + calcData.getDailyPrecipitation() + " mm");
        System.out.println("日净雨量: " + calcData.getDailyPE() + " mm");
        System.out.println("日总径流: " + calcData.getDailyR() + " mm");
        System.out.println("日出口流量: " + calcData.getDailyQ() + " m³/s");
        System.out.println("总土壤含水量: " + calcData.getW() + " mm");
    }

    @Test
    void testMultipleDaysSimulation() {
        // 创建多日输入数据
        List<XajDailyWatershedData> inputData = new ArrayList<>();
        LocalDate startDate = LocalDate.of(2024, 6, 1);
        
        // 模拟10天的降雨蒸发数据
        double[] precipitations = {25.0, 15.0, 0.0, 5.0, 35.0, 10.0, 0.0, 20.0, 8.0, 12.0};
        double[] evaporations = {4.0, 5.0, 6.0, 5.5, 4.5, 5.0, 6.5, 5.5, 6.0, 5.0};
        
        for (int i = 0; i < 10; i++) {
            XajDailyWatershedData data = new XajDailyWatershedData(
                startDate.plusDays(i), 
                precipitations[i], 
                evaporations[i]
            );
            inputData.add(data);
        }

        // 执行模拟
        List<XajDailyResult> results = calculator.simulate(inputData);

        // 验证结果
        assertNotNull(results);
        assertEquals(10, results.size());

        System.out.println("\n多日模拟结果：");
        System.out.printf("%-12s %-8s %-8s %-8s %-10s %-10s%n", 
            "日期", "降雨量", "蒸发量", "径流", "流量", "土壤含水");
        
        for (XajDailyResult result : results) {
            assertTrue(result.getDailyQ() >= 0, "流量应大于等于0");
            assertTrue(result.getW() > 0, "土壤含水量应大于0");
            
            System.out.printf("%-12s %-8.1f %-8.1f %-8.2f %-10.2f %-10.2f%n",
                result.getReportDate(),
                result.getDailyP(),
                result.getDailyE(),
                result.getDailyR(),
                result.getDailyQ(),
                result.getW()
            );
        }

        // 验证单位线计算结果
        List<XajDailyUnitHydrographResultData> uhResults = calculator.getDailyUnitHydrographResultList();
        if (uhResults != null && !uhResults.isEmpty()) {
            System.out.println("\n单位线汇流结果：");
            System.out.printf("%-12s %-10s%n", "日期", "汇流流量");
            for (XajDailyUnitHydrographResultData uhResult : uhResults) {
                System.out.printf("%-12s %-10.2f%n",
                    uhResult.getReportDate(),
                    uhResult.getQ()
                );
            }
        }
    }

    @Test
    void testDryPeriod() {
        // 测试干旱期计算
        XajDailyWatershedData data = new XajDailyWatershedData(
            LocalDate.of(2024, 6, 1), 
            0.0,  // 无降雨
            6.0   // 较高蒸发
        );
        
        List<XajDailyWatershedData> inputData = List.of(data);
        List<XajDailyResult> results = calculator.simulate(inputData);

        XajDailyResult result = results.get(0);
        
        assertEquals(0.0, result.getDailyR(), 0.001, "干旱期径流应为0");
        assertEquals(0.0, result.getDailyPE(), 0.001, "干旱期净雨量应为0");
        assertTrue(result.getW() > 0, "土壤含水量应大于0");

        System.out.println("\n干旱期计算结果：");
        System.out.println("日径流量: " + result.getDailyR() + " mm");
        System.out.println("日流量: " + result.getDailyQ() + " m³/s");
        System.out.println("土壤含水量: " + result.getW() + " mm");
    }

    @Test
    void testHeavyRainfall() {
        // 测试暴雨期计算
        XajDailyWatershedData data = new XajDailyWatershedData(
            LocalDate.of(2024, 6, 1), 
            100.0, // 大降雨
            5.0    // 正常蒸发
        );
        
        List<XajDailyWatershedData> inputData = List.of(data);
        List<XajDailyResult> results = calculator.simulate(inputData);

        XajDailyResult result = results.get(0);
        
        assertTrue(result.getDailyR() > 0, "暴雨期径流应大于0");
        assertTrue(result.getDailyPE() > 0, "暴雨期净雨量应大于0");
        assertTrue(result.getDailyQ() > 0, "暴雨期流量应大于0");

        System.out.println("\n暴雨期计算结果：");
        System.out.println("日降雨量: " + result.getDailyP() + " mm");
        System.out.println("日径流量: " + result.getDailyR() + " mm");
        System.out.println("日流量: " + result.getDailyQ() + " m³/s");
        System.out.println("土壤含水量: " + result.getW() + " mm");
    }

    @Test
    void testParameterValidation() {
        // 测试参数验证
        assertThrows(IllegalArgumentException.class, () -> {
            new XajDailyCalculator(null, null, unitHydrographList);
        });

        // 测试负降雨量
        XajDailyCalcData calcData = new XajDailyCalcData();
        calcData.setDailyPrecipitation(-10.0);
        calcData.setDailyEvaporation(5.0);
        
        assertThrows(IllegalArgumentException.class, () -> {
            calculator.calculate(calcData, null);
        });
    }
}