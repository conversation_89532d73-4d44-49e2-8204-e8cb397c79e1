package com.yutoudev.irrigation.irr.controller.admin.allocationplanmonth.vo;

import lombok.*;

import java.time.*;
import java.math.BigDecimal;

import com.yutoudev.irrigation.irr.controller.admin.userbase.vo.UserBaseExcelVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseExcelVO;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseExcelVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseExcelVO;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.experimental.Accessors;

/**
 *
 * 配水调度月计划ExcelVO
 * @description 管理后台-配水调度月计划导出、导入ExcelVO
 * <AUTHOR>
 * @time 2024-07-23 14:54:39
 *
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class AllocationPlanMonthExcelVO {


    /**
     * ID
     * 
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 计划名称
     * 
     */
    @ExcelProperty("计划名称")
    private String name;

    /**
     * 用水用户ID
     * 
     */
    @ExcelProperty("用水用户ID")
    private Long userId;

    /**
     * 用水用户
     * 
     */
    @ExcelProperty("用水用户")
    private UserBaseExcelVO user;

    /**
     * 灌溉片区
     * 
     */
    @ExcelProperty("灌溉片区")
    private ZoneBaseExcelVO zone;

    /**
     * 灌溉片区ID
     * 
     */
    @ExcelProperty("灌溉片区ID")
    private Long zoneId;

    /**
     * 水渠
     * 
     */
    @ExcelProperty("水渠")
    private ChanBaseExcelVO chan;

    /**
     * 水渠ID
     * 
     */
    @ExcelProperty("水渠ID")
    private Long chanId;

    /**
     * 水源
     * 
     */
    @ExcelProperty("水源")
    private SwhsBaseExcelVO swhs;

    /**
     * 水源ID
     * 
     */
    @ExcelProperty("水源ID")
    private Long swhsId;

    /**
     * 计划供水
     * @mock （m³）
     */
    @ExcelProperty("计划供水")
    private BigDecimal supplyWater;

    /**
     * 备注
     * 
     */
    @ExcelProperty("备注")
    private String note;

    /**
     * 计划时间
     * @mock （yyyy-MM）
     */
    @ExcelProperty("计划时间")
    private String planTime;

    /**
     * 创建时间
     * 
     */
    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;
}
