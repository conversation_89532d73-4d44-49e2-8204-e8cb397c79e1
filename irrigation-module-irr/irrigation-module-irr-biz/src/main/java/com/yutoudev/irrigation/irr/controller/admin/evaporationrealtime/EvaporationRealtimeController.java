package com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.evaporationrealtime.vo.*;
import com.yutoudev.irrigation.irr.convert.evaporationrealtime.EvaporationRealtimeConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.evaporationrealtime.EvaporationRealtimeDO;
import com.yutoudev.irrigation.irr.service.evaporationrealtime.EvaporationRealtimeService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 实时蒸散发量
 *
 * <AUTHOR>
 * @description 管理后台-实时蒸散发量controller
 * @time 2024-12-20 17:34:41
 */
@RestController
@RequestMapping("/irr/evaporation-realtime")
@Validated
public class EvaporationRealtimeController {

    private static final String MODULE_NAME = "实时蒸散发量";

    @Resource
    private EvaporationRealtimeService<EvaporationRealtimeDO> evaporationRealtimeService;

    /**
     * 创建实时蒸散发量
     *
     * @param createReqVO EvaporationRealtimeCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody EvaporationRealtimeCreateReqVO createReqVO) {
        return success(evaporationRealtimeService.create(createReqVO));
    }

    /**
     * 批量创建实时蒸散发量
     *
     * @param lists EvaporationRealtimeCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<EvaporationRealtimeCreateReqVO> lists) {
        return success(evaporationRealtimeService.createBatch(lists));
    }

    /**
     * 更新实时蒸散发量
     *
     * @param updateReqVO EvaporationRealtimeUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody EvaporationRealtimeUpdateReqVO updateReqVO) {
        evaporationRealtimeService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新实时蒸散发量
     *
     * @param lists 批量更新列表 EvaporationRealtimeUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<EvaporationRealtimeUpdateReqVO> lists) {
        return success(evaporationRealtimeService.updateBatch(lists));
    }

    /**
     * 删除实时蒸散发量
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        evaporationRealtimeService.delete(id);
        return success(true);
    }

    /**
     * 批量删除实时蒸散发量
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(evaporationRealtimeService.deleteBatch(ids));
    }

    /**
     * 获得实时蒸散发量详情
     *
     * @param id 编号 Long
     * @return CommonResult<EvaporationRealtimeDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<EvaporationRealtimeDetailRespVO> get(@RequestParam("id") Long id) {
        EvaporationRealtimeDO evaporationRealtime = evaporationRealtimeService.get(id);
        return success(EvaporationRealtimeConvert.INSTANCE.convertDetail(evaporationRealtime));
    }

    /**
     * 实时蒸散发量列表
     *
     * @param queryReqVO 查询条件 EvaporationRealtimeQueryReqVO
     * @return CommonResult<List < EvaporationRealtimeRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<EvaporationRealtimeRespVO>> getList(@RequestQueryParam EvaporationRealtimeQueryReqVO queryReqVO) {
        List<EvaporationRealtimeDO> list = evaporationRealtimeService.getList(queryReqVO);
        return success(EvaporationRealtimeConvert.INSTANCE.convertList(list));
    }

    /**
     * 实时蒸散发量分页
     *
     * @param pageVO 查询条件 EvaporationRealtimePageReqVO
     * @return CommonResult<PageResult < EvaporationRealtimeRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<EvaporationRealtimeRespVO>> page(@RequestQueryParam EvaporationRealtimePageReqVO pageVO) {
        PageResult<EvaporationRealtimeDO> pageResult = evaporationRealtimeService.page(pageVO);
        return success(EvaporationRealtimeConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出实时蒸散发量Excel
     *
     * @param queryReqVO 查询条件 EvaporationRealtimeExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam EvaporationRealtimeExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<EvaporationRealtimeDO> list = evaporationRealtimeService.getList(queryReqVO);
        // 导出 Excel
        List<EvaporationRealtimeExcelVO> datas = EvaporationRealtimeConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "实时蒸散发量", "xlsx"), queryReqVO.getExportSheetName(),
                EvaporationRealtimeExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入实时蒸散发量模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "实时蒸散发量-导入模版.xls", "sheet1", EvaporationRealtimeExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入实时蒸散发量Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:evaporation-realtime:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<EvaporationRealtimeExcelVO> list = ExcelUtils.read(file, EvaporationRealtimeExcelVO.class);
        return success(evaporationRealtimeService.importExcel(list, isUpdate));
    }
}