package com.yutoudev.irrigation.irr.controller.large.flood;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.large.flood.vo.RoutingReservoirItemLargeRespVO;
import com.yutoudev.irrigation.irr.controller.large.flood.vo.RoutingReservoirLargeRespVO;
import com.yutoudev.irrigation.irr.convert.flood.RoutingReservoirConvert;
import com.yutoudev.irrigation.irr.convert.flood.RoutingReservoirItemConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.flood.RoutingReservoirDO;
import com.yutoudev.irrigation.irr.dal.dataobject.flood.RoutingReservoirItemDO;
import com.yutoudev.irrigation.irr.service.flood.RoutingReservoirItemService;
import com.yutoudev.irrigation.irr.service.flood.RoutingReservoirService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 水库调洪演算
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/routing-reservoir")
@Validated
public class LargeRoutingReservoirController {

    private static final String MODULE_NAME = "水库调洪演算";

    @Resource
    private RoutingReservoirService<RoutingReservoirDO> routingReservoirService;

    @Resource
    private RoutingReservoirItemService<RoutingReservoirItemDO> routingReservoirItemService;

    /**
     * 获得最后流域降雨产流过程详情
     */
    @GetMapping("/last")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<RoutingReservoirLargeRespVO> getLast() {
        RoutingReservoirDO last = routingReservoirService.getLast();
        RoutingReservoirLargeRespVO respVO = RoutingReservoirConvert.INSTANCE.convertLargeRespVO(last);
        String routingLabel = MessageFormat.format("{0}年，第{1}次调洪演算", respVO.getYear(), respVO.getCode());
        respVO.setRoutingLabel(routingLabel);

        List<RoutingReservoirItemDO> itemList = routingReservoirItemService.getListByProcessId(last.getId());
        List<RoutingReservoirItemLargeRespVO> itemRespList = RoutingReservoirItemConvert.INSTANCE.convertLargeRespList(itemList);
        respVO.setItems(itemRespList);
        return success(respVO);
    }

}