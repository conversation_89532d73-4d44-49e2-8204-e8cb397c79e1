package com.yutoudev.irrigation.irr.controller.admin.inleinversionmodel.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InletInversionModelRespVo {

    /**
     * 配水计划ID，多个以逗号隔开
     */
    private String planId;

    /**
     * 水源ID
     */
    private Long swhsId;

    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 储水量（亿方）
     */
    private Double storageWater;

    /**
     * 水渠ID
     */
    private Long chanId;

    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 配水量
     */
    private Double water;

    /**
     * 配水流量
     */
    private Double flowRate;

    /**
     * 子级水渠ID
     */
    private Long childChanId;

    /**
     * 上级水渠ID
     */
    private Long parentChanId;

    /**
     * 上级水渠名称
     */
    private String parentChanName;

    /**
     * 取水水口ID
     */
    private Long quGateId;

    /**
     * 取水站点ID
     */
    private Long quEquipId;

    /**
     * 取水站点名称
     */
    private String quEquipName;

    /**
     * 出水水口ID
     */
    private Long chuGateId;

    /**
     * 出水站点ID
     */
    private Long chuEquipId;

    /**
     * 出水站点名称
     */
    private String chuEquipName;

    /**
     * 闸前水深
     */
    private Double beforeWaterLevel;

    /**
     * 水位落差
     */
    private Double waterLevelDifference;

    /**
     * 渠道流速(基流+损耗)
     */
    private Double chanFlow;

    /**
     * 反演基流
     */
    private Double baseFlow;

    /**
     * 渗水损失
     */
    private Double secondLoss;

    /**
     * 蒸发水量
     */
    private Double evaporation;

    /**
     * 毛水流量
     */
    private Double loss;

    /**
     * 毛水量
     */
    private Double gross;

    /**
     * 径流时间
     */
    private Long runoffTime;

    /**
     * 设备开闸高度
     */
    private Double gateHeight;

    /**
     * 设备默认高度
     */
    private Double gateDefaultHeight;

    /**
     * 反演情况：0异常，1通过
     */
    private Integer status;

    /**
     * 情况说明
     */
    private String reason;

    /**
     * 是否为关水闸
     */
    private Boolean isCloseWaterGate;
}
