package com.yutoudev.irrigation.irr.controller.admin.equipculvertsluice.vo;

import lombok.Data;
import lombok.ToString;

/**
 *
 * 站点涵闸配置ResponseVO
 * @description 管理后台-站点涵闸配置ResponseVO
 * <AUTHOR>
 * @time 2024-08-17 23:16:50
 *
 */
@Data
@ToString(callSuper = true)
public class EquipCulvertSluiceRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 涵闸类型
     * 
     */
    private Integer gateType;

    /**
     * 涵闸分组
     * 
     */
    private Integer gateGroup;

    /**
     * 墙面类型
     * 
     */
    private Integer wallType;

    /**
     * 闸涵孔宽
     * @mock 米
     */
    private Double holeWidth;

    /**
     * 涵洞孔高
     * @mock 米
     */
    private Double holeHeight;

    /**
     * 圆管的内半径
     * 
     */
    private Double innerRadius;

    /**
     * 扇形闸门半径
     * 
     */
    private Double fanGateRadius;

    /**
     * 扇形闸门转动轴心距闸床高度
     * 
     */
    private Double fanGcgbHeight;
}